/**
 * Edge-safe subscription validation utilities
 * 
 * This file contains subscription validation logic that can be used in Edge Runtime
 * (middleware) without importing Node.js-specific modules like crypto.
 */

// Subscription status constants
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  AUTHENTICATED: 'authenticated',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
  CREATED: 'created',
  EXPIRED: 'expired',
  HALTED: 'halted',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  TRIAL: 'trial'
} as const;

// Plan ID constants
export const PLAN_IDS = {
  FREE: 'free',
  GROWTH: 'growth',
  PRO: 'pro',
  ENTERPRISE: 'enterprise'
} as const;

export type SubscriptionStatus = typeof SUBSCRIPTION_STATUS[keyof typeof SUBSCRIPTION_STATUS];
export type PlanId = typeof PLAN_IDS[keyof typeof PLAN_IDS];

/**
 * Edge-safe subscription state manager
 * Contains core logic without crypto dependencies
 */
export class EdgeSubscriptionStateManager {
  /**
   * Determines if a subscription status should have active subscription flag set
   */
  static shouldHaveActiveSubscription(status: string, planId: string = PLAN_IDS.FREE): boolean {
    // Free plan users never have active subscription flag
    if (planId === PLAN_IDS.FREE) {
      return false;
    }

    // Trial users don't have active subscription flag (they're not paying yet)
    if (status === SUBSCRIPTION_STATUS.TRIAL) {
      return false;
    }

    // Only active status for paid plans has active subscription
    // Authenticated users have selected a plan but haven't paid yet
    return status === SUBSCRIPTION_STATUS.ACTIVE;
  }

  /**
   * Determines the access level for a user
   */
  static getAccessLevel(status: string, planId: string = PLAN_IDS.FREE): 'free' | 'trial' | 'paid' {
    if (planId === PLAN_IDS.FREE) {
      return 'free';
    }

    if (status === SUBSCRIPTION_STATUS.TRIAL) {
      return 'trial';
    }

    if (status === SUBSCRIPTION_STATUS.ACTIVE || status === SUBSCRIPTION_STATUS.AUTHENTICATED) {
      return 'paid';
    }

    return 'free';
  }

  /**
   * Checks if a status is a terminal state
   */
  static isTerminalStatus(status: string): boolean {
    const terminalStatuses: string[] = [
      SUBSCRIPTION_STATUS.CANCELLED,
      SUBSCRIPTION_STATUS.COMPLETED,
      SUBSCRIPTION_STATUS.EXPIRED
    ];
    return terminalStatuses.includes(status);
  }

  /**
   * Checks if a subscription is in trial status
   */
  static isTrialStatus(status: string): boolean {
    return status === SUBSCRIPTION_STATUS.TRIAL;
  }

  /**
   * Checks if a subscription is on free plan
   */
  static isFreeStatus(_status: string, planId?: string): boolean {
    return planId === PLAN_IDS.FREE;
  }

  /**
   * Checks if a subscription is an active paid subscription
   */
  static isActivePaidSubscription(status: string, planId: string = PLAN_IDS.FREE): boolean {
    if (planId === PLAN_IDS.FREE) {
      return false;
    }

    return status === SUBSCRIPTION_STATUS.ACTIVE || status === SUBSCRIPTION_STATUS.AUTHENTICATED;
  }
}

/**
 * Subscription state validation result
 */
export interface SubscriptionStateValidation {
  isValid: boolean;
  hasActiveSubscription: boolean;
  accessLevel: 'free' | 'trial' | 'paid';
  warnings: string[];
  errors: string[];
}

/**
 * Edge-safe subscription state validation
 * 
 * Validates subscription state consistency without importing crypto-dependent modules
 */
export function validateSubscriptionState(
  businessProfile: {
    has_active_subscription: boolean;
    trial_end_date: string | null;
  },
  paymentSubscription: {
    subscription_status: string;
    plan_id: string;
  } | null
): SubscriptionStateValidation {
  const warnings: string[] = [];
  const errors: string[] = [];

  // Determine expected state based on payment subscription
  let expectedHasActiveSubscription = false;
  let accessLevel: 'free' | 'trial' | 'paid' = 'free';

  if (paymentSubscription) {
    expectedHasActiveSubscription = EdgeSubscriptionStateManager.shouldHaveActiveSubscription(
      paymentSubscription.subscription_status,
      paymentSubscription.plan_id
    );

    accessLevel = EdgeSubscriptionStateManager.getAccessLevel(
      paymentSubscription.subscription_status,
      paymentSubscription.plan_id
    );
  }

  // Check for inconsistencies
  if (businessProfile.has_active_subscription !== expectedHasActiveSubscription) {
    errors.push(
      `has_active_subscription mismatch: business_profiles=${businessProfile.has_active_subscription}, expected=${expectedHasActiveSubscription}`
    );
  }

  // Check trial state consistency
  if (paymentSubscription?.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {
    if (!businessProfile.trial_end_date) {
      warnings.push('Trial status but no trial_end_date set');
    } else {
      const trialEnd = new Date(businessProfile.trial_end_date);
      const now = new Date();
      if (trialEnd <= now) {
        warnings.push('Trial status but trial period has expired');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    hasActiveSubscription: expectedHasActiveSubscription,
    accessLevel,
    warnings,
    errors
  };
}
