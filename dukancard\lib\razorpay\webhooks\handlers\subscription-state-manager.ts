import { PLAN_IDS, SUBSCRIPTION_STATUS } from "./subscription-constants";

/**
 * CENTRALIZED SUBSCRIPTION STATE MANAGER
 *
 * This class provides the single source of truth for ALL subscription logic.
 * Use this instead of scattered functions throughout the codebase.
 *
 * CRITICAL: This is the ONLY place where subscription access logic should be defined.
 */
export class SubscriptionStateManager {
  /**
   * MASTER FUNCTION: Determines if a user should have active subscription access
   * This is the ONLY function that should be used for subscription access control
   *
   * @param status The subscription status
   * @param planId The plan ID (required for accurate determination)
   * @returns true if the user should have active subscription features
   */
  static shouldHaveActiveSubscription(status: string, planId: string = PLAN_IDS.FREE): boolean {
    // Free plan users NEVER have "active subscription" - they're on free tier
    if (planId === PLAN_IDS.FREE) {
      return false;
    }

    // Trial users NEVER have "active subscription" - they're testing, not paying
    if (status === SUBSCRIPTION_STATUS.TRIAL) {
      return false;
    }

    // Only ACTIVE status on PAID plans counts as "active subscription"
    // Authenticated users have selected a plan but haven't paid yet
    const activeStatuses: string[] = [
      SUBSCRIPTION_STATUS.ACTIVE
    ];

    return activeStatuses.includes(status);
  }

  /**
   * Determines if a subscription status is considered terminal (final state)
   * Terminal states cannot transition to active states without creating new subscription
   */
  static isTerminalStatus(status: string): boolean {
    const terminalStatuses: string[] = [
      SUBSCRIPTION_STATUS.CANCELLED,
      SUBSCRIPTION_STATUS.EXPIRED,
      SUBSCRIPTION_STATUS.COMPLETED
    ];
    return terminalStatuses.includes(status);
  }

  /**
   * Determines if a subscription status indicates trial period
   */
  static isTrialStatus(status: string): boolean {
    return status === SUBSCRIPTION_STATUS.TRIAL;
  }

  /**
   * Determines if a plan is a free plan
   */
  static isFreeStatus(status: string, planId?: string): boolean {
    return planId === PLAN_IDS.FREE || status === 'free';
  }

  /**
   * Get user's access level based on subscription state
   */
  static getAccessLevel(status: string, planId: string = PLAN_IDS.FREE): 'free' | 'trial' | 'paid' {
    if (planId === PLAN_IDS.FREE) return 'free';
    if (status === SUBSCRIPTION_STATUS.TRIAL) return 'trial';
    if (this.shouldHaveActiveSubscription(status, planId)) return 'paid';
    return 'free';
  }

  /**
   * Determines if a subscription status indicates an active paid subscription
   */
  static isActivePaidSubscription(status: string, planId: string = PLAN_IDS.FREE): boolean {
    return this.shouldHaveActiveSubscription(status, planId);
  }

  /**
   * Validates if a status transition is allowed
   */
  static isValidStatusTransition(fromStatus: string, toStatus: string): boolean {
    // Terminal states cannot transition to non-terminal states
    if (this.isTerminalStatus(fromStatus) && !this.isTerminalStatus(toStatus)) {
      return false;
    }

    // All other transitions are allowed
    return true;
  }
}

// Legacy function wrappers for backward compatibility - DEPRECATED
// Use SubscriptionStateManager instead for new code
export function shouldHaveActiveSubscription(status: string): boolean {
  console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');
  return SubscriptionStateManager.shouldHaveActiveSubscription(status, PLAN_IDS.FREE);
}

export function isTerminalStatus(status: string): boolean {
  return SubscriptionStateManager.isTerminalStatus(status);
}

export function isActivePaidSubscription(status: string): boolean {
  console.warn('[DEPRECATED] Use SubscriptionStateManager.isActivePaidSubscription(status, planId) instead');
  return SubscriptionStateManager.isActivePaidSubscription(status, PLAN_IDS.FREE);
}

export function isTrialStatus(status: string): boolean {
  return SubscriptionStateManager.isTrialStatus(status);
}

export function isFreeStatus(status: string, planId?: string): boolean {
  return SubscriptionStateManager.isFreeStatus(status, planId);
}

/**
 * LEGACY FUNCTION - Use SubscriptionStateManager.shouldHaveActiveSubscription instead
 *
 * @deprecated Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead
 */
export function shouldHaveActiveSubscriptionByPlan(status: string, planId: string): boolean {
  console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');
  return SubscriptionStateManager.shouldHaveActiveSubscription(status, planId);
}