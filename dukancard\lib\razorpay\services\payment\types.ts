/**
 * Razorpay Payment Service Types
 *
 * This file contains type definitions for the Razorpay payment service.
 */

import { RazorpayApiResponse } from "../subscription/types";

// Re-export the RazorpayApiResponse type
export type { RazorpayApiResponse };

/**
 * Razorpay refund parameters
 */
export interface RefundPaymentParams {
  /**
   * The amount to be refunded (in the smallest unit of currency).
   * For example, if you want to refund ₹500, the value will be 50000.
   * If not provided, the entire payment amount will be refunded.
   */
  amount?: number;

  /**
   * The speed at which the refund is to be processed.
   *
   * Options:
   * - "normal": Processed within 5-7 business days.
   * - "optimum": Processed at optimal speed based on Razorpay's internal fund transfer logic.
   *   If the refund can be processed instantly, Razorpay will do so, irrespective of the payment method.
   *   If an instant refund is not possible, Razorpay will process it at normal speed.
   *
   * Default is "normal".
   */
  speed?: "normal" | "optimum";

  /**
   * Key-value pairs used to store additional information.
   * A maximum of 15 key-value pairs can be included.
   */
  notes?: Record<string, string>;

  /**
   * A unique identifier provided by you for your internal reference.
   */
  receipt?: string;
}

/**
 * Razorpay refund response
 */
export interface RazorpayRefund {
  /**
   * The unique identifier of the refund.
   */
  id: string;

  /**
   * Indicates the type of entity.
   */
  entity: string;

  /**
   * The amount refunded (in the smallest unit of currency).
   */
  amount: number;

  /**
   * The currency of the refund.
   */
  currency: string;

  /**
   * The unique identifier of the payment for which the refund is initiated.
   */
  payment_id: string;

  /**
   * Key-value pairs used to store additional information.
   */
  notes: Record<string, string>;

  /**
   * A unique identifier provided by you for your internal reference.
   */
  receipt: string | null;

  /**
   * A dynamic array consisting of a unique reference number (either RRN, ARN or UTR)
   * that is provided by the banking partner when a refund is processed.
   */
  acquirer_data: {
    arn: string | null;
  };

  /**
   * Unix timestamp at which the refund was created.
   */
  created_at: number;

  /**
   * This parameter is populated if the refund was created as part of a batch upload.
   */
  batch_id: string | null;

  /**
   * Indicates the state of the refund.
   * Possible values: pending, processed, failed
   */
  status: string;

  /**
   * The processing mode of the refund seen in the refund response.
   *
   * Possible values:
   * - "instant": Indicates that the refund has been processed instantly via fund transfer.
   * - "normal": Indicates that the refund has been processed by the payment processing partner.
   *   The refund will take 5-7 working days.
   */
  speed_processed: string;

  /**
   * The processing mode of the refund requested.
   *
   * Possible values:
   * - "normal": Indicates that the refund will be processed via the normal speed.
   * - "optimum": Indicates that the refund will be processed at an optimal speed based on
   *   Razorpay's internal fund transfer logic.
   */
  speed_requested: string;
}

/**
 * Razorpay refund API response
 */
export type RefundPaymentResponse = RazorpayApiResponse<RazorpayRefund>;

/**
 * Razorpay payment response
 */
export interface RazorpayPayment {
  /**
   * The unique identifier of the payment.
   */
  id: string;

  /**
   * Indicates the type of entity.
   */
  entity: string;

  /**
   * The payment amount in currency subunits. For example, for an amount of ₹1.00 enter 100.
   */
  amount: number;

  /**
   * The currency in which the payment is made.
   */
  currency: string;

  /**
   * The status of the payment. Possible values:
   * created, authorized, captured, refunded, failed
   */
  status: string;

  /**
   * The payment method used for making the payment. Possible values:
   * card, netbanking, wallet, emi, upi
   */
  method: string;

  /**
   * Order id, if provided.
   */
  order_id: string | null;

  /**
   * Description of the payment, if any.
   */
  description: string | null;

  /**
   * Indicates whether the payment is done via an international card or a domestic one.
   */
  international: boolean;

  /**
   * The refund status of the payment. Possible values:
   * null, partial, full
   */
  refund_status: string | null;

  /**
   * The amount refunded in currency subunits.
   */
  amount_refunded: number;

  /**
   * Indicates if the payment is captured.
   */
  captured: boolean;

  /**
   * Customer email address used for the payment.
   */
  email: string | null;

  /**
   * Customer contact number used for the payment.
   */
  contact: string | null;

  /**
   * Fee (including GST) charged by Razorpay.
   */
  fee: number;

  /**
   * GST charged for the payment.
   */
  tax: number;

  /**
   * Error that occurred during payment.
   */
  error_code: string | null;

  /**
   * Description of the error that occurred during payment.
   */
  error_description: string | null;

  /**
   * The point of failure.
   */
  error_source: string | null;

  /**
   * The stage where the transaction failure occurred.
   */
  error_step: string | null;

  /**
   * The exact error reason.
   */
  error_reason: string | null;

  /**
   * Contains user-defined fields, stored for reference purposes.
   */
  notes: Record<string, string>;

  /**
   * Timestamp, in UNIX format, on which the payment was created.
   */
  created_at: number;

  /**
   * The unique identifier of the card used by the customer to make the payment.
   */
  card_id: string | null;

  /**
   * The 4-character bank code which the customer's account is associated with.
   */
  bank: string | null;

  /**
   * The name of the wallet used by the customer to make the payment.
   */
  wallet: string | null;

  /**
   * The customer's VPA (Virtual Payment Address) or UPI id used to make the payment.
   */
  vpa: string | null;

  /**
   * A dynamic array consisting of unique reference numbers.
   */
  acquirer_data: Record<string, unknown>;

  /**
   * UPI details if the payment method is UPI.
   */
  upi?: {
    payer_account_type?: string;
    vpa?: string;
    flow?: string;
  };
}
