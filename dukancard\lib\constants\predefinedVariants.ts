/**
 * Simplified predefined variant types and options for common business needs
 * Focused on essential variants that most businesses actually use
 */

// VariantType interface definition (since it was removed from types/variants.ts)
interface VariantType {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  is_predefined: boolean;
  sort_order: number;
}

export interface PredefinedVariantOption {
  value: string;
  display_value: string;
  color_code?: string;
  description?: string;
  sort_order: number;
}

// ============================================================================
// VARIANT TYPES - Essential variants only
// ============================================================================

export const PREDEFINED_VARIANT_TYPES: Omit<VariantType, 'created_at' | 'updated_at'>[] = [
  // Most Common Variants
  { id: 'size', name: 'size', display_name: 'Size', description: 'Product size variations (XS, S, M, L, XL, etc.)', is_predefined: true, sort_order: 1 },
  { id: 'color', name: 'color', display_name: 'Color', description: 'Product color variations', is_predefined: true, sort_order: 2 },
  { id: 'material', name: 'material', display_name: 'Material', description: 'Product material (cotton, silk, metal, wood, etc.)', is_predefined: true, sort_order: 3 },
  { id: 'style', name: 'style', display_name: 'Style', description: 'Product style variations (classic, modern, vintage, etc.)', is_predefined: true, sort_order: 4 },
  { id: 'pattern', name: 'pattern', display_name: 'Pattern', description: 'Product pattern or design (solid, striped, floral, etc.)', is_predefined: true, sort_order: 5 },

  // Technical Specifications
  { id: 'capacity', name: 'capacity', display_name: 'Capacity', description: 'Storage or volume capacity (100ml, 1L, 32GB, etc.)', is_predefined: true, sort_order: 6 },
  { id: 'weight', name: 'weight', display_name: 'Weight', description: 'Product weight variations (100g, 1kg, light, heavy, etc.)', is_predefined: true, sort_order: 7 },
  { id: 'power', name: 'power', display_name: 'Power', description: 'Power rating (5W, 25W, 100W, etc.)', is_predefined: true, sort_order: 8 },
  { id: 'voltage', name: 'voltage', display_name: 'Voltage', description: 'Electrical voltage (12V, 24V, 110V, 220V, etc.)', is_predefined: true, sort_order: 9 },
  { id: 'frequency', name: 'frequency', display_name: 'Frequency', description: 'Operating frequency (50Hz, 60Hz, etc.)', is_predefined: true, sort_order: 10 },

  // Dimensions & Measurements
  { id: 'length', name: 'length', display_name: 'Length', description: 'Product length (10cm, 1m, 6ft, etc.)', is_predefined: true, sort_order: 11 },
  { id: 'width', name: 'width', display_name: 'Width', description: 'Product width (5cm, 50cm, 2ft, etc.)', is_predefined: true, sort_order: 12 },
  { id: 'height', name: 'height', display_name: 'Height', description: 'Product height (10cm, 1m, 3ft, etc.)', is_predefined: true, sort_order: 13 },
  { id: 'diameter', name: 'diameter', display_name: 'Diameter', description: 'Product diameter (5cm, 10cm, 1ft, etc.)', is_predefined: true, sort_order: 14 },
  { id: 'thickness', name: 'thickness', display_name: 'Thickness', description: 'Product thickness (1mm, 5mm, 1cm, etc.)', is_predefined: true, sort_order: 15 },

  // Food & Consumables
  { id: 'flavor', name: 'flavor', display_name: 'Flavor', description: 'Taste or flavor variations (vanilla, chocolate, spicy, etc.)', is_predefined: true, sort_order: 16 },
  { id: 'quantity', name: 'quantity', display_name: 'Quantity', description: 'Package quantity or count (1 piece, pack of 6, bulk, etc.)', is_predefined: true, sort_order: 17 },
  { id: 'expiry', name: 'expiry', display_name: 'Expiry', description: 'Expiry or shelf life (6 months, 1 year, etc.)', is_predefined: true, sort_order: 18 },
  { id: 'ingredients', name: 'ingredients', display_name: 'Ingredients', description: 'Key ingredients or composition', is_predefined: true, sort_order: 19 },

  // Beauty & Personal Care
  { id: 'shade', name: 'shade', display_name: 'Shade', description: 'Color shade or tone (fair, medium, dark, etc.)', is_predefined: true, sort_order: 20 },
  { id: 'skin_type', name: 'skin_type', display_name: 'Skin Type', description: 'Suitable skin type (normal, dry, oily, sensitive, etc.)', is_predefined: true, sort_order: 21 },
  { id: 'coverage', name: 'coverage', display_name: 'Coverage', description: 'Coverage level (light, medium, full, etc.)', is_predefined: true, sort_order: 22 },
  { id: 'spf', name: 'spf', display_name: 'SPF', description: 'Sun protection factor (SPF 15, SPF 30, SPF 50, etc.)', is_predefined: true, sort_order: 23 },

  // Clothing Specific
  { id: 'fit', name: 'fit', display_name: 'Fit', description: 'Clothing fit type (slim, regular, loose, oversized, etc.)', is_predefined: true, sort_order: 24 },
  { id: 'sleeve', name: 'sleeve', display_name: 'Sleeve', description: 'Sleeve type or length (sleeveless, short, full, etc.)', is_predefined: true, sort_order: 25 },
  { id: 'collar', name: 'collar', display_name: 'Collar', description: 'Collar type (round neck, V-neck, polo, etc.)', is_predefined: true, sort_order: 26 },
  { id: 'occasion', name: 'occasion', display_name: 'Occasion', description: 'Suitable occasion (casual, formal, party, sports, etc.)', is_predefined: true, sort_order: 27 },
  { id: 'season', name: 'season', display_name: 'Season', description: 'Suitable season (summer, winter, all-season, etc.)', is_predefined: true, sort_order: 28 },

  // Technology & Electronics
  { id: 'connectivity', name: 'connectivity', display_name: 'Connectivity', description: 'Connection type (WiFi, Bluetooth, USB, etc.)', is_predefined: true, sort_order: 29 },
  { id: 'compatibility', name: 'compatibility', display_name: 'Compatibility', description: 'Device compatibility (iPhone, Android, Windows, etc.)', is_predefined: true, sort_order: 30 },
  { id: 'resolution', name: 'resolution', display_name: 'Resolution', description: 'Display resolution (HD, Full HD, 4K, etc.)', is_predefined: true, sort_order: 31 },
  { id: 'storage', name: 'storage', display_name: 'Storage', description: 'Storage capacity (16GB, 64GB, 256GB, etc.)', is_predefined: true, sort_order: 32 },
  { id: 'ram', name: 'ram', display_name: 'RAM', description: 'Memory capacity (4GB, 8GB, 16GB, etc.)', is_predefined: true, sort_order: 33 },

  // General Product Attributes
  { id: 'type', name: 'type', display_name: 'Type', description: 'Product type or variant (standard, premium, deluxe, etc.)', is_predefined: true, sort_order: 34 },
  { id: 'finish', name: 'finish', display_name: 'Finish', description: 'Product surface finish (matte, glossy, textured, etc.)', is_predefined: true, sort_order: 35 },
  { id: 'grade', name: 'grade', display_name: 'Grade', description: 'Quality grade (A, B, premium, standard, etc.)', is_predefined: true, sort_order: 36 },
  { id: 'brand', name: 'brand', display_name: 'Brand', description: 'Product brand or manufacturer', is_predefined: true, sort_order: 37 },
  { id: 'model', name: 'model', display_name: 'Model', description: 'Product model or version', is_predefined: true, sort_order: 38 },
  { id: 'age_group', name: 'age_group', display_name: 'Age Group', description: 'Target age group (kids, teens, adults, seniors, etc.)', is_predefined: true, sort_order: 39 },
  { id: 'gender', name: 'gender', display_name: 'Gender', description: 'Target gender (men, women, unisex, etc.)', is_predefined: true, sort_order: 40 },

  // Specialized Categories
  { id: 'fragrance', name: 'fragrance', display_name: 'Fragrance', description: 'Scent or fragrance type (floral, woody, fresh, etc.)', is_predefined: true, sort_order: 41 },
  { id: 'texture', name: 'texture', display_name: 'Texture', description: 'Product texture (smooth, rough, soft, hard, etc.)', is_predefined: true, sort_order: 42 },
  { id: 'temperature', name: 'temperature', display_name: 'Temperature', description: 'Operating temperature (hot, cold, room temp, etc.)', is_predefined: true, sort_order: 43 },
  { id: 'certification', name: 'certification', display_name: 'Certification', description: 'Product certifications (organic, ISO, FDA approved, etc.)', is_predefined: true, sort_order: 44 },
  { id: 'warranty', name: 'warranty', display_name: 'Warranty', description: 'Warranty period (1 year, 2 years, lifetime, etc.)', is_predefined: true, sort_order: 45 },
];

// ============================================================================
// SIZE OPTIONS - REMOVED: Users will enter sizes manually
// ============================================================================
// All size options have been removed to allow manual input

// ============================================================================
// COLOR OPTIONS
// ============================================================================

export const COLOR_OPTIONS: PredefinedVariantOption[] = [
  // Basic Colors
  { value: 'black', display_value: 'Black', color_code: '#000000', sort_order: 1 },
  { value: 'white', display_value: 'White', color_code: '#FFFFFF', sort_order: 2 },
  { value: 'gray', display_value: 'Gray', color_code: '#808080', sort_order: 3 },
  { value: 'light_gray', display_value: 'Light Gray', color_code: '#D3D3D3', sort_order: 4 },
  { value: 'dark_gray', display_value: 'Dark Gray', color_code: '#404040', sort_order: 5 },
  { value: 'charcoal', display_value: 'Charcoal', color_code: '#36454F', sort_order: 6 },

  // Red Variants
  { value: 'red', display_value: 'Red', color_code: '#FF0000', sort_order: 7 },
  { value: 'dark_red', display_value: 'Dark Red', color_code: '#8B0000', sort_order: 8 },
  { value: 'light_red', display_value: 'Light Red', color_code: '#FFB6C1', sort_order: 9 },
  { value: 'crimson', display_value: 'Crimson', color_code: '#DC143C', sort_order: 10 },
  { value: 'maroon', display_value: 'Maroon', color_code: '#800000', sort_order: 11 },
  { value: 'burgundy', display_value: 'Burgundy', color_code: '#800020', sort_order: 12 },

  // Blue Variants
  { value: 'blue', display_value: 'Blue', color_code: '#0000FF', sort_order: 13 },
  { value: 'navy', display_value: 'Navy', color_code: '#000080', sort_order: 14 },
  { value: 'light_blue', display_value: 'Light Blue', color_code: '#ADD8E6', sort_order: 15 },
  { value: 'dark_blue', display_value: 'Dark Blue', color_code: '#00008B', sort_order: 16 },
  { value: 'sky_blue', display_value: 'Sky Blue', color_code: '#87CEEB', sort_order: 17 },
  { value: 'royal_blue', display_value: 'Royal Blue', color_code: '#4169E1', sort_order: 18 },
  { value: 'turquoise', display_value: 'Turquoise', color_code: '#40E0D0', sort_order: 19 },
  { value: 'teal', display_value: 'Teal', color_code: '#008080', sort_order: 20 },
  { value: 'aqua', display_value: 'Aqua', color_code: '#00FFFF', sort_order: 21 },

  // Green Variants
  { value: 'green', display_value: 'Green', color_code: '#008000', sort_order: 22 },
  { value: 'dark_green', display_value: 'Dark Green', color_code: '#006400', sort_order: 23 },
  { value: 'light_green', display_value: 'Light Green', color_code: '#90EE90', sort_order: 24 },
  { value: 'lime', display_value: 'Lime', color_code: '#00FF00', sort_order: 25 },
  { value: 'olive', display_value: 'Olive', color_code: '#808000', sort_order: 26 },
  { value: 'forest_green', display_value: 'Forest Green', color_code: '#228B22', sort_order: 27 },
  { value: 'mint', display_value: 'Mint', color_code: '#98FB98', sort_order: 28 },

  // Yellow/Orange Variants
  { value: 'yellow', display_value: 'Yellow', color_code: '#FFFF00', sort_order: 29 },
  { value: 'light_yellow', display_value: 'Light Yellow', color_code: '#FFFFE0', sort_order: 30 },
  { value: 'orange', display_value: 'Orange', color_code: '#FFA500', sort_order: 31 },
  { value: 'dark_orange', display_value: 'Dark Orange', color_code: '#FF8C00', sort_order: 32 },
  { value: 'coral', display_value: 'Coral', color_code: '#FF7F50', sort_order: 33 },
  { value: 'peach', display_value: 'Peach', color_code: '#FFCBA4', sort_order: 34 },

  // Purple/Pink Variants
  { value: 'purple', display_value: 'Purple', color_code: '#800080', sort_order: 35 },
  { value: 'dark_purple', display_value: 'Dark Purple', color_code: '#4B0082', sort_order: 36 },
  { value: 'light_purple', display_value: 'Light Purple', color_code: '#DDA0DD', sort_order: 37 },
  { value: 'violet', display_value: 'Violet', color_code: '#8A2BE2', sort_order: 38 },
  { value: 'lavender', display_value: 'Lavender', color_code: '#E6E6FA', sort_order: 39 },
  { value: 'indigo', display_value: 'Indigo', color_code: '#4B0082', sort_order: 40 },
  { value: 'pink', display_value: 'Pink', color_code: '#FFC0CB', sort_order: 41 },
  { value: 'hot_pink', display_value: 'Hot Pink', color_code: '#FF69B4', sort_order: 42 },
  { value: 'magenta', display_value: 'Magenta', color_code: '#FF00FF', sort_order: 43 },

  // Brown Variants
  { value: 'brown', display_value: 'Brown', color_code: '#A52A2A', sort_order: 44 },
  { value: 'dark_brown', display_value: 'Dark Brown', color_code: '#654321', sort_order: 45 },
  { value: 'light_brown', display_value: 'Light Brown', color_code: '#D2B48C', sort_order: 46 },
  { value: 'tan', display_value: 'Tan', color_code: '#D2B48C', sort_order: 47 },
  { value: 'beige', display_value: 'Beige', color_code: '#F5F5DC', sort_order: 48 },
  { value: 'cream', display_value: 'Cream', color_code: '#FFFDD0', sort_order: 49 },
  { value: 'ivory', display_value: 'Ivory', color_code: '#FFFFF0', sort_order: 50 },

  // Metallic Colors
  { value: 'gold', display_value: 'Gold', color_code: '#FFD700', sort_order: 51 },
  { value: 'silver', display_value: 'Silver', color_code: '#C0C0C0', sort_order: 52 },
  { value: 'bronze', display_value: 'Bronze', color_code: '#CD7F32', sort_order: 53 },
  { value: 'copper', display_value: 'Copper', color_code: '#B87333', sort_order: 54 },
  { value: 'rose_gold', display_value: 'Rose Gold', color_code: '#E8B4B8', sort_order: 55 },

  // Special
  { value: 'multicolor', display_value: 'Multicolor', sort_order: 56 },
];

// ============================================================================
// MATERIAL, STYLE, PATTERN, FINISH OPTIONS - REMOVED: Users will enter values manually
// ============================================================================
// All non-color variant options have been removed to allow manual input

// ============================================================================
// ALL OTHER VARIANT OPTIONS - REMOVED: Users will enter values manually
// ============================================================================
// All non-color variant options (flavor, capacity, weight, power, quantity,
// shade, skin_type, fit, sleeve, type) have been removed to allow manual input

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Get predefined options for a specific variant type
 * Only colors have predefined options for swatches, all other types use manual input
 */
export const getPredefinedOptionsForType = (variantTypeName: string): PredefinedVariantOption[] => {
  switch (variantTypeName.toLowerCase()) {
    case 'color':
      return COLOR_OPTIONS;
    // All other variant types now use manual input instead of predefined options
    case 'size':
    case 'material':
    case 'style':
    case 'pattern':
    case 'capacity':
    case 'weight':
    case 'power':
    case 'voltage':
    case 'frequency':
    case 'length':
    case 'width':
    case 'height':
    case 'diameter':
    case 'thickness':
    case 'flavor':
    case 'quantity':
    case 'expiry':
    case 'ingredients':
    case 'shade':
    case 'skin_type':
    case 'coverage':
    case 'spf':
    case 'fit':
    case 'sleeve':
    case 'collar':
    case 'occasion':
    case 'season':
    case 'connectivity':
    case 'compatibility':
    case 'resolution':
    case 'storage':
    case 'ram':
    case 'type':
    case 'finish':
    case 'grade':
    case 'brand':
    case 'model':
    case 'age_group':
    case 'gender':
    case 'fragrance':
    case 'texture':
    case 'temperature':
    case 'certification':
    case 'warranty':
    default:
      return [];
  }
};

/**
 * Get all available variant types
 */
export const getAllVariantTypes = (): Omit<VariantType, 'created_at' | 'updated_at'>[] => {
  return PREDEFINED_VARIANT_TYPES;
};

/**
 * Check if a variant type has predefined options
 * Only color variants have predefined options for swatches
 */
export const hasPredefineOptions = (variantTypeName: string): boolean => {
  return variantTypeName.toLowerCase() === 'color';
};

/**
 * Search variant options by query string
 */
export const searchVariantOptions = (variantTypeName: string, query: string): PredefinedVariantOption[] => {
  const options = getPredefinedOptionsForType(variantTypeName);
  if (!query) return options;

  const lowerQuery = query.toLowerCase();
  return options.filter(option =>
    option.display_value.toLowerCase().includes(lowerQuery) ||
    option.value.toLowerCase().includes(lowerQuery)
  );
};

/**
 * Get variant type by name
 */
export const getVariantTypeByName = (name: string): Omit<VariantType, 'created_at' | 'updated_at'> | null => {
  return PREDEFINED_VARIANT_TYPES.find(type => type.name === name.toLowerCase()) || null;
};
