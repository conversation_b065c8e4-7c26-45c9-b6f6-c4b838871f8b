import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../utils/auth";

export interface PaymentDetails {
  id: string;
  amount: number;
  currency: string;
  status: string;
  method: string; // "upi", "netbanking", "card", etc.
  created_at: number;
  captured: boolean;
  description?: string;
  email?: string;
  contact?: string;
  notes?: Record<string, string>;
}

export interface PaymentResponse {
  success: boolean;
  data?: PaymentDetails;
  error?: string;
}

export interface RefundPaymentParams {
  amount?: number;
  speed?: 'normal' | 'optimum';
  notes?: Record<string, string>;
  receipt?: string;
}

export interface RazorpayRefund {
  id: string;
  entity: string;
  amount: number;
  currency: string;
  payment_id: string;
  notes: Record<string, string>;
  receipt: string | null;
  acquirer_data: {
    arn: string | null;
  };
  created_at: number;
  batch_id: string | null;
  status: string;
  speed_processed: string;
  speed_requested: string;
}

export interface RefundPaymentResponse {
  success: boolean;
  data?: RazorpayRefund;
  error?: string;
}

export interface RazorpayPayment {
  id: string;
  entity: string;
  amount: number;
  currency: string;
  status: string;
  order_id: string | null;
  invoice_id: string | null;
  international: boolean;
  method: string;
  amount_refunded: number;
  refund_status: string | null;
  captured: boolean;
  description: string | null;
  card_id: string | null;
  bank: string | null;
  wallet: string | null;
  vpa: string | null;
  email: string;
  contact: string;
  notes: Record<string, string>;
  fee: number | null;
  tax: number | null;
  error_code: string | null;
  error_description: string | null;
  error_source: string | null;
  error_step: string | null;
  error_reason: string | null;
  acquirer_data: Record<string, unknown>;
  created_at: number;
}

/**
 * Get payment details from Razorpay
 * @param paymentId The payment ID to fetch details for
 * @returns Payment details or error
 */
export async function getPaymentDetails(paymentId: string): Promise<PaymentResponse> {
  try {
    console.log(`[RAZORPAY_PAYMENT] Fetching payment details for ID: ${paymentId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/payments/${paymentId}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const payment = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_PAYMENT] Error fetching payment:', payment);
      return {
        success: false,
        error: payment.error?.description || "Failed to fetch payment details"
      };
    }

    console.log(`[RAZORPAY_PAYMENT] Payment details fetched successfully:`, {
      id: payment.id,
      method: payment.method,
      status: payment.status,
      amount: payment.amount,
      currency: payment.currency
    });

    return {
      success: true,
      data: {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        method: payment.method,
        created_at: payment.created_at,
        captured: payment.captured,
        description: payment.description,
        email: payment.email,
        contact: payment.contact,
        notes: payment.notes
      }
    };
  } catch (error) {
    console.error(`[RAZORPAY_PAYMENT] Error fetching payment details:`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch payment details"
    };
  }
}

/**
 * Get payment details from Razorpay (alias for getPaymentDetails)
 * @param paymentId The payment ID to fetch details for
 * @returns Payment details or error
 */
export async function getPayment(paymentId: string): Promise<PaymentResponse> {
  return getPaymentDetails(paymentId);
}

/**
 * Refund a payment
 * @param paymentId The payment ID to refund
 * @param params Refund parameters
 * @returns Refund details or error
 */
export async function refundPayment(paymentId: string, params: RefundPaymentParams = {}): Promise<RefundPaymentResponse> {
  try {
    console.log(`[RAZORPAY_REFUND] Initiating refund for payment ID: ${paymentId}`, params);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/payments/${paymentId}/refund`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    // Parse response
    const refund = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_REFUND] Error processing refund:', refund);
      return {
        success: false,
        error: refund.error?.description || "Failed to process refund"
      };
    }

    console.log(`[RAZORPAY_REFUND] Refund processed successfully:`, {
      id: refund.id,
      amount: refund.amount,
      status: refund.status,
      payment_id: refund.payment_id
    });

    return {
      success: true,
      data: refund
    };
  } catch (error) {
    console.error(`[RAZORPAY_REFUND] Error processing refund:`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to process refund"
    };
  }
}
