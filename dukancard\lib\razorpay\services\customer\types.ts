/**
 * Types for Razorpay customer service
 */

import { RazorpayApiResponse } from "../subscription/types";

/**
 * Razorpay customer interface
 */
export interface RazorpayCustomer {
  id: string;
  entity: string;
  name: string;
  email: string;
  contact: string;
  gstin?: string;
  notes?: Record<string, string>;
  created_at: number;
}

/**
 * Parameters for creating a customer
 */
export interface CreateCustomerParams {
  name: string;
  email: string;
  contact: string;
  gstin?: string;
  notes?: Record<string, string>;
  fail_existing?: 0 | 1;
}

/**
 * Parameters for updating a customer
 */
export interface UpdateCustomerParams {
  name?: string;
  email?: string;
  contact?: string;
  gstin?: string;
  notes?: Record<string, string>;
}

/**
 * Parameters for fetching customers
 */
export interface FetchCustomersParams {
  count?: number;
  skip?: number;
}

/**
 * Response for fetching customers
 */
export interface FetchCustomersResponse {
  entity: string;
  count: number;
  items: RazorpayCustomer[];
}

/**
 * Type for customer API responses
 */
export type CustomerApiResponse = RazorpayApiResponse<RazorpayCustomer>;

/**
 * Type for customers list API responses
 */
export type CustomersListApiResponse = RazorpayApiResponse<FetchCustomersResponse>;
