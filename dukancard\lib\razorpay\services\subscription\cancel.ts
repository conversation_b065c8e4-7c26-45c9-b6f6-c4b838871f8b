/**
 * Razorpay Subscription Cancellation
 *
 * This file contains functions for cancelling Razorpay subscriptions.
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { RazorpayApiResponse, RazorpaySubscription, CancelSubscriptionParams } from "./types";

/**
 * Cancel a subscription in Razorpay
 *
 * API Endpoint: POST https://api.razorpay.com/v1/subscriptions/:id/cancel
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param subscriptionId The subscription ID
 * @param cancelAtCycleEnd Whether to cancel at the end of the current billing cycle
 * @returns The cancelled subscription or error
 */
export async function cancelSubscription(
  subscriptionId: string,
  cancelAtCycleEnd: boolean = false
): Promise<RazorpayApiResponse<RazorpaySubscription>> {
  try {
    console.log(`[RAZORPAY_DEBUG] Cancelling subscription with ID: ${subscriptionId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Prepare request body
    const body: CancelSubscriptionParams = cancelAtCycleEnd ? { cancel_at_cycle_end: 1 } : {};

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error cancelling subscription:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully cancelled subscription: ${data.id}`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception cancelling subscription:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
