/**
 * Razorpay Subscription Scheduled Changes
 *
 * This file contains functions for managing scheduled changes to Razorpay subscriptions.
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { RazorpayApiResponse, RazorpaySubscription } from "./types";

/**
 * Retrieve scheduled changes for a subscription in Razorpay
 *
 * API Endpoint: GET https://api.razorpay.com/v1/subscriptions/:id/retrieve_scheduled_changes
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param subscriptionId The subscription ID
 * @returns The subscription with scheduled changes or error
 *
 * Example response:
 * ```json
 * {
 *   "id":"sub_00000000000001",
 *   "entity":"subscription",
 *   "plan_id":"plan_00000000000003",
 *   "customer_id":"cust_00000000000001",
 *   "status":"active",
 *   "current_start":1580284732,
 *   "current_end":1580841000,
 *   "ended_at":null,
 *   "quantity":25,
 *   "notes":{
 *     "notes_key_1":"<PERSON>, <PERSON>, <PERSON>",
 *     "notes_key_2":"<PERSON>, <PERSON>… decaf."
 *   },
 *   "charge_at":1580841000,
 *   "start_at":1580284732,
 *   "end_at":1611081000,
 *   "auth_attempts":0,
 *   "total_count":6,
 *   "paid_count":1,
 *   "customer_notify":true,
 *   "created_at":1580284702,
 *   "expire_by":1580626111,
 *   "short_url":"https://rzp.io/i/fFWTkbf",
 *   "has_scheduled_changes":true,
 *   "change_scheduled_at":1557253800,
 *   "source": "api",
 *   "offer_id":"offer_JHD834hjbxzhd38d",
 *   "remaining_count":5
 * }
 * ```
 */
export async function getScheduledChanges(
  subscriptionId: string
): Promise<RazorpayApiResponse<RazorpaySubscription>> {
  try {
    console.log(`[RAZORPAY_DEBUG] Fetching scheduled changes for subscription with ID: ${subscriptionId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/retrieve_scheduled_changes`, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching scheduled changes:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully fetched scheduled changes for subscription: ${data.id}`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching scheduled changes:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}

/**
 * Cancel scheduled changes for a subscription in Razorpay
 *
 * API Endpoint: POST https://api.razorpay.com/v1/subscriptions/:id/cancel_scheduled_changes
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param subscriptionId The subscription ID
 * @returns The subscription with cancelled scheduled changes or error
 *
 * Example response:
 * ```json
 * {
 *   "id": "sub_00000000000001",
 *   "entity": "subscription",
 *   "plan_id": "plan_00000000000001",
 *   "customer_id": "cust_00000000000001",
 *   "status": "active",
 *   "current_start": 1580284732,
 *   "current_end": 1580841000,
 *   "ended_at": null,
 *   "quantity": 1,
 *   "notes": {
 *     "notes_key_1": "Tea, Earl Grey, Hot",
 *     "notes_key_2": "Tea, Earl Grey… decaf."
 *   },
 *   "charge_at": 1580841000,
 *   "start_at": 1580284732,
 *   "end_at": 1611081000,
 *   "auth_attempts": 0,
 *   "total_count": 6,
 *   "paid_count": 1,
 *   "customer_notify": true,
 *   "created_at": 1580284702,
 *   "expire_by": 1580626111,
 *   "short_url": "https://rzp.io/i/fFWTkbf",
 *   "has_scheduled_changes": false,
 *   "change_scheduled_at": null,
 *   "source": "api",
 *   "offer_id": "offer_JHD834hjbxzhd38d",
 *   "remaining_count": 5
 * }
 * ```
 */
export async function cancelScheduledChanges(
  subscriptionId: string
): Promise<RazorpayApiResponse<RazorpaySubscription>> {
  try {
    console.log(`[RAZORPAY_DEBUG] Cancelling scheduled changes for subscription with ID: ${subscriptionId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/cancel_scheduled_changes`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error cancelling scheduled changes:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully cancelled scheduled changes for subscription: ${data.id}`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception cancelling scheduled changes:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
