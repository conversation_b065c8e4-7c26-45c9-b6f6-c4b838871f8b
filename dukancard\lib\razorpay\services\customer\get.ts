/**
 * Get customer details from Razorpay
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { CustomerApiResponse, CustomersListApiResponse, FetchCustomersParams } from "./types";

/**
 * Get a customer from Razorpay by ID
 *
 * API Endpoint: GET https://api.razorpay.com/v1/customers/:id
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param customerId The customer ID
 * @returns The customer details or error
 */
export async function getCustomer(
  customerId: string
): Promise<CustomerApiResponse> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/customers/${customerId}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching customer:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching customer:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}

/**
 * List all customers from Razorpay
 *
 * API Endpoint: GET https://api.razorpay.com/v1/customers
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param params Optional query parameters (count, skip, etc.)
 * @returns List of customers or error
 */
export async function listCustomers(
  params: FetchCustomersParams = {}
): Promise<CustomersListApiResponse> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Build query string
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    // Make API request
    const url = `${RAZORPAY_API_URL.replace('/v2', '/v1')}/customers${queryString ? `?${queryString}` : ''}`;
    const response = await fetch(url, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching customers:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching customers:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}

/**
 * Find a customer by email
 *
 * This function fetches all customers and filters by email
 * Note: Razorpay API doesn't support direct filtering by email
 *
 * @param email The email to search for
 * @returns The customer if found, or null
 */
export async function findCustomerByEmail(
  email: string
): Promise<CustomerApiResponse> {
  try {

    // Get all customers (with a reasonable limit)
    const result = await listCustomers({ count: 100 });

    if (!result.success) {
      // Return a properly typed error response
      return {
        success: false,
        error: result.error
      };
    }

    // Check if result.data exists
    if (!result.data) {
      return {
        success: false,
        error: {
          message: 'Invalid response format from Razorpay',
          code: 'INVALID_RESPONSE',
          type: 'ERROR'
        }
      };
    }

    // Find customer with matching email
    const customer = result.data.items.find(
      (customer) => customer.email.toLowerCase() === email.toLowerCase()
    );

    if (customer) {
      return { success: true, data: customer };
    }
    return {
      success: false,
      error: {
        message: `No customer found with email: ${email}`,
        code: 'CUSTOMER_NOT_FOUND',
        type: 'NOT_FOUND'
      }
    };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception finding customer by email:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
