/**
 * Razorpay API Types
 *
 * This file contains types for Razorpay API responses and webhook payloads.
 */

// Webhook event data type
export interface RazorpayWebhookData {
  entity: string;
  account_id: string;
  event: string;
  contains: string[];
  payload: {
    payment?: RazorpayPayment;
    subscription?: RazorpaySubscription;
    refund?: RazorpayRefund;
    order?: RazorpayOrder;
    invoice?: RazorpayInvoice;
  };
  created_at: number;
}

// Razorpay subscription interface
export interface RazorpaySubscription {
  id: string;
  entity: string;
  plan_id: string;
  customer_id: string;
  status: string;
  current_start: number;
  current_end: number;
  ended_at: number | null;
  quantity: number;
  notes: Record<string, string>;
  charge_at: number;
  start_at: number;
  end_at: number | null;
  auth_attempts: number;
  total_count: number;
  paid_count: number;
  customer_notify: boolean;
  created_at: number;
  expire_by: number | null;
  short_url: string;
  has_scheduled_changes: boolean;
  change_scheduled_at: number | null;
  source: string;
  payment_method: string;
  offer_id: string | null;
  remaining_count: number;
}

// Razorpay payment interface
export interface RazorpayPayment {
  entity: {
    id: string;
    entity: string;
    amount: number;
    currency: string;
    status: string;
    order_id: string;
    invoice_id: string | null;
    international: boolean;
    method: string;
    amount_refunded: number;
    refund_status: string | null;
    captured: boolean;
    description: string;
    card_id: string | null;
    bank: string | null;
    wallet: string | null;
    vpa: string | null;
    email: string;
    contact: string;
    notes: Record<string, string>;
    fee: number;
    tax: number;
    error_code: string | null;
    error_description: string | null;
    error_source: string | null;
    error_step: string | null;
    error_reason: string | null;
    acquirer_data: {
      rrn: string;
      upi_transaction_id: string;
    };
    created_at: number;
  };
}

// Razorpay order interface
export interface RazorpayOrder {
  entity: {
    id: string;
    entity: string;
    amount: number;
    amount_paid: number;
    amount_due: number;
    currency: string;
    receipt: string;
    offer_id: string | null;
    status: string;
    attempts: number;
    notes: Record<string, string>;
    created_at: number;
  };
}

// Razorpay refund interface
export interface RazorpayRefund {
  entity: {
    id: string;
    entity: string;
    amount: number;
    currency: string;
    payment_id: string;
    notes: Record<string, string>;
    receipt: string | null;
    acquirer_data: {
      arn: string | null;
    };
    created_at: number;
    batch_id: string | null;
    status: string;
    speed_processed: string;
    speed_requested: string;
  };
}

// Razorpay invoice interface
export interface RazorpayInvoice {
  entity: {
    id: string;
    entity: string;
    receipt: string | null;
    invoice_number: string | null;
    customer_id: string;
    customer_details: {
      id: string;
      name: string;
      email: string;
      contact: string;
      gstin: string | null;
      billing_address: string | null;
      shipping_address: string | null;
      customer_name: string;
      customer_email: string;
      customer_contact: string;
    };
    order_id: string;
    subscription_id: string;
    payment_id: string;
    status: string;
    expire_by: number | null;
    issued_at: number;
    paid_at: number | null;
    cancelled_at: number | null;
    expired_at: number | null;
    sms_status: string | null;
    email_status: string | null;
    date: number;
    terms: string | null;
    partial_payment: boolean;
    gross_amount: number;
    tax_amount: number;
    taxable_amount: number;
    amount: number;
    amount_paid: number;
    amount_due: number;
    currency: string;
    currency_symbol: string;
    description: string | null;
    notes: Record<string, string>;
    comment: string | null;
    short_url: string;
    view_less: boolean;
    billing_start: number | null;
    billing_end: number | null;
    type: string;
    created_at: number;
  };
}

// Example webhook payload for subscription.charged event
/*
{
  "entity": "event",
  "account_id": "acc_JOGUdtKu3dB03d",
  "event": "subscription.charged",
  "contains": ["payment", "subscription"],
  "payload": {
    "payment": {
      "entity": {
        "id": "pay_JebiXkKGYwua5L",
        "entity": "payment",
        "amount": 10000,
        "currency": "INR",
        "status": "captured",
        "order_id": "order_JebiXkKGYwua5L",
        "invoice_id": null,
        "international": false,
        "method": "upi",
        "amount_refunded": 0,
        "refund_status": null,
        "captured": true,
        "description": "Subscription payment for plan: basic-plan-monthly",
        "card_id": null,
        "bank": null,
        "wallet": null,
        "vpa": "success@razorpay",
        "email": "<EMAIL>",
        "contact": "+************",
        "notes": {
          "subscription_id": "sub_JebiXkKGYwua5L"
        },
        "fee": 236,
        "tax": 36,
        "error_code": null,
        "error_description": null,
        "error_source": null,
        "error_step": null,
        "error_reason": null,
        "acquirer_data": {
          "rrn": "*********",
          "upi_transaction_id": "RAZORPAY*********"
        },
        "created_at": **********
      }
    },
    "subscription": {
      "id": "sub_JebiXkKGYwua5L",
      "entity": "subscription",
      "plan_id": "plan_JebiXkKGYwua5L",
      "customer_id": "cust_JebiXkKGYwua5L",
      "status": "active",
      "current_start": **********,
      "current_end": **********,
      "ended_at": null,
      "quantity": 1,
      "notes": {
        "business_id": "*********"
      },
      "charge_at": **********,
      "start_at": **********,
      "end_at": null,
      "auth_attempts": 1,
      "total_count": 12,
      "paid_count": 1,
      "customer_notify": true,
      "created_at": **********,
      "expire_by": null,
      "short_url": "https://rzp.io/i/abcdefgh",
      "has_scheduled_changes": false,
      "change_scheduled_at": null,
      "source": "api",
      "payment_method": "upi",
      "offer_id": null,
      "remaining_count": 11
    }
  },
  "created_at": **********
}
*/
