/**
 * Create a customer in Razorpay
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { CreateCustomerParams, CustomerApiResponse } from "./types";

/**
 * Create a customer in Razorpay
 *
 * API Endpoint: POST https://api.razorpay.com/v1/customers
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param params The customer parameters
 * @returns The created customer or error
 *
 * Example usage:
 * ```
 * const result = await createCustomer({
 *   name: '<PERSON>',
 *   email: '<EMAIL>',
 *   contact: '9999999999',
 *   notes: {
 *     note_key_1: 'Note value 1',
 *     note_key_2: 'Note value 2'
 *   }
 * });
 * ```
 */
export async function createCustomer(
  params: CreateCustomerParams
): Promise<CustomerApiResponse> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/customers`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error creating customer:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception creating customer:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
