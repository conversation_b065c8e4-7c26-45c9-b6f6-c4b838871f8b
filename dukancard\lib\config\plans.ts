/**
 * Central configuration for all plan-related constants
 * This file serves as the single source of truth for plan information
 */

// Plan types
export type PlanType = "free" | "basic" | "growth" | "pro" | "enterprise";
export type PlanCycle = "monthly" | "yearly";
export type PaymentGateway = "razorpay";

// Plan features
export interface PlanFeature {
  name: string;
  included: boolean;
  limit?: number | "unlimited";
  description?: string;
}

// Define Razorpay plan IDs
// Different plan IDs for production and development environments
const RAZORPAY_PLAN_IDS = {
  free: {
    monthly: "free-plan-monthly", // Free plan doesn't need a real Razorpay ID
    yearly: "free-plan-yearly",   // Free plan doesn't need a real Razorpay ID
  },
  basic: {
    monthly: process.env.NODE_ENV === 'production'
      ? "plan_QO9rDMTSLeT34b" // Production Basic monthly
      : "plan_QRgoJF3OfM6mB0", // Development Basic monthly
    yearly: process.env.NODE_ENV === 'production'
      ? "plan_QO9sfgFBnEFATA" // Production Basic yearly
      : "plan_QRgr1XzaksqvSZ", // Development Basic yearly
  },
  growth: {
    monthly: process.env.NODE_ENV === 'production'
      ? "plan_QbnOd77S3FVeUc" // Production Growth monthly
      : "plan_QbnMGvYCN7BM0V", // Development Growth monthly
    yearly: process.env.NODE_ENV === 'production'
      ? "plan_QbnOuE7iogOGTq" // Production Growth yearly
      : "plan_QbnMdSbSeFrykv", // Development Growth yearly
  },
  pro: {
    monthly: process.env.NODE_ENV === 'production'
      ? "plan_QbnP83wvmzUOqM" // Production Pro monthly
      : "plan_QbnN4mwUu6H2Ho", // Development Pro monthly
    yearly: process.env.NODE_ENV === 'production'
      ? "plan_QbnPJinNt66Pik" // Production Pro yearly
      : "plan_QbnNYfrCExI496", // Development Pro yearly
  },
  enterprise: {
    monthly: "enterprise-plan-monthly-razorpay", // Placeholder for future implementation
    yearly: "enterprise-plan-yearly-razorpay", // Placeholder for future implementation
  }
};

// Set payment gateway to Razorpay
const _paymentGateway: PaymentGateway = "razorpay";


// Plan interface
export interface Plan {
  id: PlanType;
  name: string;
  description: string;
  features: PlanFeature[];
  razorpayPlanIds: {
    monthly: string;
    yearly: string;
  };
  pricing: {
    monthly: number;
    yearly: number;
  };
  recommended?: boolean;
}

// Plan definitions
export const PLANS: Plan[] = [
  {
    id: "free",
    name: "Free",
    description: "Basic features for individuals and startups",
    razorpayPlanIds: RAZORPAY_PLAN_IDS.free,
    pricing: {
      monthly: 0,
      yearly: 0,
    },
    features: [
      {
        name: "Digital Business Card",
        included: true,
        description: "Simple digital business card with contact information",
      },
      {
        name: "QR Code for Sharing",
        included: true,
        description: "Shareable QR code for your business card",
      },
      {
        name: "Social Media Links",
        included: true,
        description: "Add links to your social media profiles",
      },
      {
        name: "Product Listings",
        included: true,
        limit: 5,
        description: "Showcase your products or services (limited to 5)",
      },
      {
        name: "Customer Subscriptions",
        included: true,
        description: "Allow customers to subscribe to your business",
      },
      {
        name: "Ratings & Reviews",
        included: true,
        description: "Collect and display customer reviews",
      },
      {
        name: "Like Feature",
        included: true,
        description: "Let customers like your business card",
      },
      {
        name: "Basic Analytics",
        included: false,
        description: "View basic metrics like views and clicks",
      },
      {
        name: "Default Theme",
        included: true,
        description: "Use the default Dukancard theme",
      },
      {
        name: "Delivery Hours",
        included: true,
        description: "Set and display your delivery hours",
      },
      {
        name: "Business Hours",
        included: true,
        description: "Set and display your business hours",
      },
      {
        name: "Theme Customization",
        included: false,
        description: "Customize your card theme and colors",
      },

      {
        name: "Enhanced Analytics",
        included: false,
        description: "View detailed metrics including product views",
      },
      {
        name: "Advanced Analytics",
        included: false,
        description: "Access comprehensive business insights",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 1,
        description: "Upload and display 1 image in your gallery",
      },
      {
        name: "Dukancard Branding",
        included: true,
        description: "Dukancard branding on your business card",
      },
    ],
  },
  {
    id: "basic",
    name: "Basic",
    description: "Essential features for small businesses",
    recommended: false,
    razorpayPlanIds: RAZORPAY_PLAN_IDS.basic,
    pricing: {
      monthly: 99,
      yearly: 999,
    },
    features: [
      {
        name: "Digital Business Card",
        included: true,
        description: "Basic digital business card with contact information",
      },
      {
        name: "QR Code for Sharing",
        included: true,
        description: "Shareable QR code for your business card",
      },
      {
        name: "Social Media Links",
        included: true,
        description: "Add links to your social media profiles",
      },
      {
        name: "Product Listings",
        included: true,
        limit: 15,
        description: "Showcase your products or services (up to 15)",
      },
      {
        name: "Customer Subscriptions",
        included: true,
        description: "Allow customers to subscribe to your business",
      },
      {
        name: "Ratings & Reviews",
        included: true,
        description: "Collect and display customer reviews",
      },
      {
        name: "Like Feature",
        included: true,
        description: "Let customers like your business card",
      },
      {
        name: "Basic Analytics",
        included: true,
        description: "View basic metrics like views and clicks",
      },
      {
        name: "Default Theme",
        included: true,
        description: "Use the default Dukancard theme",
      },
      {
        name: "Delivery Hours",
        included: true,
        description: "Set and display your delivery hours",
      },
      {
        name: "Business Hours",
        included: true,
        description: "Set and display your business hours",
      },
      {
        name: "Theme Customization",
        included: false,
        description: "Customize your card theme and colors",
      },

      {
        name: "Enhanced Analytics",
        included: false,
        description: "View detailed metrics including product views",
      },
      {
        name: "Advanced Analytics",
        included: false,
        description: "Access comprehensive business insights",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 3,
        description: "Upload and display up to 3 images in your gallery",
      },
      {
        name: "Dukancard Branding",
        included: true,
        description: "Dukancard branding on your business card",
      },
    ],
  },
  {
    id: "growth",
    name: "Growth",
    description: "Advanced features for growing businesses",
    recommended: true,
    razorpayPlanIds: RAZORPAY_PLAN_IDS.growth,
    pricing: {
      monthly: 499,
      yearly: 4990,
    },
    features: [
      {
        name: "Digital Business Card",
        included: true,
        description: "Premium digital business card with enhanced features",
      },
      {
        name: "QR Code for Sharing",
        included: true,
        description: "Shareable QR code for your business card",
      },
      {
        name: "Social Media Links",
        included: true,
        description: "Add links to your social media profiles",
      },
      {
        name: "Product Listings",
        included: true,
        limit: 50,
        description: "Showcase your products or services (up to 50)",
      },
      {
        name: "Customer Subscriptions",
        included: true,
        description: "Allow customers to subscribe to your business",
      },
      {
        name: "Ratings & Reviews",
        included: true,
        description: "Collect and display customer reviews",
      },
      {
        name: "Like Feature",
        included: true,
        description: "Let customers like your business card",
      },
      {
        name: "Basic Analytics",
        included: true,
        description: "View basic metrics like views and clicks",
      },
      {
        name: "Default Theme",
        included: true,
        description: "Use the default Dukancard theme",
      },
      {
        name: "Delivery Hours",
        included: true,
        description: "Set and display your delivery hours",
      },
      {
        name: "Business Hours",
        included: true,
        description: "Set and display your business hours",
      },
      {
        name: "Theme Customization",
        included: false,
        description: "Customize your card theme and colors",
      },

      {
        name: "Enhanced Analytics",
        included: true,
        description: "View detailed metrics including product views",
      },
      {
        name: "Advanced Analytics",
        included: false,
        description: "Access comprehensive business insights",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 10,
        description: "Upload and display up to 10 images",
      },
      {
        name: "Dukancard Branding",
        included: true,
        description: "Dukancard branding on your business card",
      },
    ],
  },
  {
    id: "pro",
    name: "Pro",
    description: "Premium features for established businesses",
    razorpayPlanIds: RAZORPAY_PLAN_IDS.pro,
    pricing: {
      monthly: 1999,
      yearly: 19990,
    },
    features: [
      {
        name: "Digital Business Card",
        included: true,
        description: "Elite digital business card with premium features",
      },
      {
        name: "QR Code for Sharing",
        included: true,
        description: "Shareable QR code for your business card",
      },
      {
        name: "Social Media Links",
        included: true,
        description: "Add links to your social media profiles",
      },
      {
        name: "Product Listings",
        included: true,
        limit: "unlimited",
        description: "Showcase unlimited products or services",
      },
      {
        name: "Customer Subscriptions",
        included: true,
        description: "Allow customers to subscribe to your business",
      },
      {
        name: "Ratings & Reviews",
        included: true,
        description: "Collect and display customer reviews",
      },
      {
        name: "Like Feature",
        included: true,
        description: "Let customers like your business card",
      },
      {
        name: "Basic Analytics",
        included: true,
        description: "View basic metrics like views and clicks",
      },
      {
        name: "Default Theme",
        included: true,
        description: "Use the default Dukancard theme",
      },
      {
        name: "Delivery Hours",
        included: true,
        description: "Set and display your delivery hours",
      },
      {
        name: "Business Hours",
        included: true,
        description: "Set and display your business hours",
      },
      {
        name: "Theme Customization",
        included: true,
        description: "Customize your card theme and colors",
      },

      {
        name: "Enhanced Analytics",
        included: true,
        description: "View detailed metrics including product views",
      },
      {
        name: "Advanced Analytics",
        included: true,
        description: "Access comprehensive business insights",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 50,
        description: "Upload and display up to 50 images",
      },
      {
        name: "Priority Support",
        included: true,
        description: "Priority email and chat support",
      },
      {
        name: "Dukancard Branding",
        included: false,
        description: "No Dukancard branding on your business card",
      },
    ],
  },
  {
    id: "enterprise",
    name: "Enterprise",
    description: "Custom solutions for large businesses",
    razorpayPlanIds: RAZORPAY_PLAN_IDS.enterprise,
    pricing: {
      monthly: 0, // Will be handled as "Contact Sales"
      yearly: 0, // Will be handled as "Contact Sales"
    },
    features: [
      {
        name: "Digital Business Card",
        included: true,
        description:
          "Enterprise-grade digital business card with all premium features",
      },
      {
        name: "QR Code for Sharing",
        included: true,
        description: "Shareable QR code for your business card",
      },
      {
        name: "Social Media Links",
        included: true,
        description: "Add links to your social media profiles",
      },
      {
        name: "Product Listings",
        included: true,
        limit: "unlimited",
        description: "Showcase unlimited products or services",
      },
      {
        name: "Customer Subscriptions",
        included: true,
        description: "Allow customers to subscribe to your business",
      },
      {
        name: "Ratings & Reviews",
        included: true,
        description: "Collect and display customer reviews",
      },
      {
        name: "Like Feature",
        included: true,
        description: "Let customers like your business card",
      },
      {
        name: "Basic Analytics",
        included: true,
        description: "View basic metrics like views and clicks",
      },
      {
        name: "Default Theme",
        included: true,
        description: "Use the default Dukancard theme",
      },
      {
        name: "Delivery Hours",
        included: true,
        description: "Set and display your delivery hours",
      },
      {
        name: "Business Hours",
        included: true,
        description: "Set and display your business hours",
      },
      {
        name: "Theme Customization",
        included: true,
        description: "Customize your card theme and colors",
      },

      {
        name: "Enhanced Analytics",
        included: true,
        description: "View detailed metrics including product views",
      },
      {
        name: "Advanced Analytics",
        included: true,
        description: "Access comprehensive business insights",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 100,
        description: "Upload and display up to 100 images",
      },
      {
        name: "Dedicated Account Manager",
        included: true,
        description: "Get a dedicated account manager",
      },
      {
        name: "Custom Analytics Dashboard",
        included: true,
        description: "Get a custom analytics dashboard",
      },
      {
        name: "24/7 Priority Support",
        included: true,
        description: "24/7 priority support",
      },
      {
        name: "White-Label Option",
        included: true,
        description: "Use your own branding instead of Dukancard",
      },
      {
        name: "Dukancard Branding",
        included: false,
        description: "No Dukancard branding on your business card",
      },
    ],
  },
];

/**
 * Get a plan by its ID
 * @param planId The plan ID
 * @returns The plan object or undefined if not found
 */
export function getPlanById(planId: PlanType): Plan | undefined {
  return PLANS.find((plan) => plan.id === planId);
}

/**
 * Get a plan by its Razorpay plan ID
 * @param planId The Razorpay plan ID
 * @returns The plan object or undefined if not found
 */
export function getPlanByRazorpayPlanId(
  planId: string
): Plan | undefined {
  return PLANS.find(
    (plan) =>
      plan.razorpayPlanIds.monthly === planId ||
      plan.razorpayPlanIds.yearly === planId
  );
}

/**
 * Map a Razorpay plan ID to a Dukancard plan type
 * @param razorpayPlanId The Razorpay plan ID
 * @returns The corresponding Dukancard plan type or 'free' if not found
 */
export function mapRazorpayPlanToDukancardPlan(
  razorpayPlanId: string
): PlanType {
  const plan = getPlanByRazorpayPlanId(razorpayPlanId);
  return plan?.id || "free";
}

/**
 * Get the Razorpay plan ID for a given plan type and cycle
 * @param planType The plan type
 * @param planCycle The plan cycle
 * @returns The Razorpay plan ID or null if not found
 */
export function getRazorpayPlanId(
  planType: PlanType,
  planCycle: PlanCycle
): string | null {
  const plan = getPlanById(planType);
  if (!plan) {
    return null;
  }

  const planId = plan.razorpayPlanIds[planCycle];
  return planId || null;
}

/**
 * Get the Razorpay plan ID for subscription actions
 * Centralized function for all subscription-related operations
 * @param planId The plan ID
 * @param planCycle The plan cycle
 * @returns The Razorpay plan ID or throws error if invalid
 */
export function getSubscriptionRazorpayPlanId(
  planId: PlanType,
  planCycle: PlanCycle
): string {
  if (planId === "free") {
    return planCycle === "monthly" ? "free-plan-monthly" : "free-plan-yearly";
  } else if (planId === "basic") {
    return planCycle === "monthly"
      ? (process.env.NODE_ENV === 'production' ? "plan_QO9rDMTSLeT34b" : "plan_QRgoJF3OfM6mB0")
      : (process.env.NODE_ENV === 'production' ? "plan_QO9sfgFBnEFATA" : "plan_QRgr1XzaksqvSZ");
  } else if (planId === "growth") {
    return planCycle === "monthly"
      ? (process.env.NODE_ENV === 'production' ? "plan_QbnOd77S3FVeUc" : "plan_QbnMGvYCN7BM0V")
      : (process.env.NODE_ENV === 'production' ? "plan_QbnOuE7iogOGTq" : "plan_QbnMdSbSeFrykv");
  } else if (planId === "pro") {
    return planCycle === "monthly"
      ? (process.env.NODE_ENV === 'production' ? "plan_QbnP83wvmzUOqM" : "plan_QbnN4mwUu6H2Ho")
      : (process.env.NODE_ENV === 'production' ? "plan_QbnPJinNt66Pik" : "plan_QbnNYfrCExI496");
  } else {
    throw new Error(`Invalid plan selected: ${planId}`);
  }
}

/**
 * Get the plan ID for a given plan type and cycle
 * @param planType The plan type
 * @param planCycle The plan cycle
 * @returns The plan ID or undefined if not found
 */
export function getPlanId(
  planType: PlanType,
  planCycle: PlanCycle
): string | undefined {
  // Use Razorpay plan IDs since we've migrated to Razorpay
  const planId = getRazorpayPlanId(planType, planCycle);
  return planId === null ? undefined : planId;
}

/**
 * Get the product limit for a given plan type
 * @param planType The plan type
 * @returns The product limit (number or Infinity for unlimited)
 */
export function getProductLimit(planType: PlanType | null | undefined): number {
  if (!planType) return 0;

  const plan = getPlanById(planType);
  if (!plan) return 0;

  const productFeature = plan.features.find(
    (feature) => feature.name === "Product Listings"
  );
  if (!productFeature || !productFeature.included) return 0;

  return productFeature.limit === "unlimited"
    ? Infinity
    : productFeature.limit || 0;
}

/**
 * Check if a feature is included in a plan
 * @param planType The plan type
 * @param featureName The feature name
 * @returns True if the feature is included, false otherwise
 */
export function hasFeature(
  planType: PlanType | null | undefined,
  featureName: string
): boolean {
  if (!planType) return false;

  const plan = getPlanById(planType);
  if (!plan) return false;

  const feature = plan.features.find((feature) => feature.name === featureName);
  return feature?.included || false;
}

/**
 * Get all plans
 * @returns Array of plans
 */
export function pricingPlans(): Plan[] {
  return PLANS;
}
