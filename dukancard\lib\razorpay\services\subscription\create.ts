/**
 * Razorpay Subscription Creation
 *
 * This file contains functions for creating Razorpay subscriptions.
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { CreateSubscriptionParams, RazorpayApiResponse, RazorpaySubscription } from "./types";

/**
 * Create a subscription in Razorpay
 *
 * API Endpoint: POST https://api.razorpay.com/v1/subscriptions
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param params The subscription parameters
 * @returns The created subscription or error
 *
 * Example usage:
 * ```
 * const result = await createSubscription({
 *   plan_id: 'plan_00000000000001',
 *   total_count: 12,
 *   quantity: 1,
 *   customer_notify: true,
 *   notes: {
 *     note_key_1: 'Note value 1',
 *     note_key_2: 'Note value 2'
 *   }
 * });
 * ```
 */
export async function createSubscription(
  params: CreateSubscriptionParams
): Promise<RazorpayApiResponse<RazorpaySubscription>> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error creating subscription:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception creating subscription:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
