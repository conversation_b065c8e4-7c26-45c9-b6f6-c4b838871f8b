import { WebhookProcessingContext, SubscriptionState } from "../types";
import { SubscriptionStateManager, SUBSCRIPTION_STATUS } from "../../utils";
import { stateTransitionValidator } from "./stateTransitionValidator";

export class EventOrderValidator {

  async validateWebhookEventOrder(
    context: WebhookProcessingContext,
    currentState: SubscriptionState
  ): Promise<{ shouldProcess: boolean; reason: string }> {
    try {
      if (!currentState.last_webhook_timestamp) {
        return {
          shouldProcess: true,
          reason: "First webhook for subscription"
        };
      }

      const lastWebhookTimestamp = new Date(currentState.last_webhook_timestamp).getTime() / 1000;
      const currentWebhookTimestamp = context.webhookTimestamp;
      const WEBHOOK_TOLERANCE_SECONDS = 30;
      const timeDifference = currentWebhookTimestamp - lastWebhookTimestamp;

      if (context.eventType === 'subscription.authenticated' &&
          currentState.subscription_status === 'trial' &&
          currentState.plan_id !== 'free' &&
          currentState.cancelled_at) {

        console.warn(`[WEBHOOK_ORDER] Rejecting authenticated webhook for cancelled subscription ${context.subscriptionId} - likely came after cancellation`);

        return {
          shouldProcess: false,
          reason: `Authenticated webhook rejected: subscription is in trial state (likely cancelled), plan: ${currentState.plan_id}`
        };
      }

      if (timeDifference < -WEBHOOK_TOLERANCE_SECONDS) {
        return {
          shouldProcess: false,
          reason: `Webhook timestamp ${currentWebhookTimestamp} is ${Math.abs(timeDifference)} seconds older than last processed webhook ${lastWebhookTimestamp}`
        };
      }

      return {
        shouldProcess: true,
        reason: timeDifference >= 0
          ? `Webhook is newer than last processed (${timeDifference}s difference)`
          : `Webhook is within tolerance (${timeDifference}s difference, tolerance: ${WEBHOOK_TOLERANCE_SECONDS}s)`
      };

    } catch (error) {
      console.error(`[WEBHOOK_ORDER] Error validating webhook order:`, error);
      return {
        shouldProcess: true,
        reason: "Error during validation, allowing processing"
      };
    }
  }

  async shouldProcessEvent(
    context: WebhookProcessingContext,
    currentState: SubscriptionState
  ): Promise<{ should: boolean; reason: string }> {

    const sequenceValidation = stateTransitionValidator.validateWebhookSequence(context, currentState);
    if (!sequenceValidation.should) {
      return sequenceValidation;
    }

    const isTerminal = SubscriptionStateManager.isTerminalStatus(currentState.subscription_status) ||
                      currentState.plan_id === 'free';

    const terminalEventTypes = [
      'subscription.cancelled',
      'subscription.expired',
      'subscription.completed',
      'subscription.halted'
    ];

    // Special case: Allow halted subscriptions to be reactivated
    const isHaltedSubscription = currentState.subscription_status === 'halted';
    const isActivationEvent = context.eventType === 'subscription.activated';

    if (isTerminal && !terminalEventTypes.includes(context.eventType)) {
      // Allow halted subscriptions to be reactivated
      if (isHaltedSubscription && isActivationEvent) {
        // Allow this transition - halted subscriptions can be reactivated
      } else {
        return {
          should: false,
          reason: `Subscription is in terminal state (${currentState.subscription_status}, plan: ${currentState.plan_id}), ignoring ${context.eventType} event`
        };
      }
    }

    if (context.eventType === 'subscription.cancelled' &&
        currentState.subscription_status === SUBSCRIPTION_STATUS.CANCELLED) {
      // Allow processing to ensure data consistency
    }

    if (currentState.subscription_status === SUBSCRIPTION_STATUS.TRIAL &&
        (context.eventType === 'subscription.authenticated' || context.eventType === 'subscription.activated')) {
      // Allow processing - this is a valid transition
    }

    if (currentState.plan_id === 'free' &&
        (context.eventType === 'subscription.authenticated' || context.eventType === 'subscription.activated')) {
      // Allow processing - this is a valid upgrade
    }

    if (context.eventType === 'subscription.activated' &&
        currentState.subscription_status === SUBSCRIPTION_STATUS.ACTIVE) {
      return {
        should: false,
        reason: "Subscription is already active"
      };
    }

    return {
      should: true,
      reason: "Event should be processed"
    };
  }
}

export const eventOrderValidator = new EventOrderValidator();