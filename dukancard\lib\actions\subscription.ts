"use server";

// Import and re-export all subscription actions from the new modular structure
// We need to import and re-export each function individually because "use server" files
// can only export async functions directly

// Import subscription creation actions
import {
  createSubscription as createSubscriptionAction,
  cancelAndCreateSubscription as cancelAndCreateSubscriptionAction
} from "./subscription/create";

// Import subscription management actions
import {
  cancelSubscription as cancelSubscriptionAction,
  changePlan as changePlanAction,
  changePlanWithManage as changePlanWithManageAction,
  scheduleSubscriptionChange as scheduleSubscriptionChangeAction,
  manageSubscription as manageSubscriptionAction,
  switchAuthenticatedSubscription as switchAuthenticatedSubscriptionAction
} from "./subscription/manage";

// Import the new switchActiveSubscription function directly
import {
  switchActiveSubscription as switchActiveSubscriptionAction,
  switchActiveSubscriptionWithNewPayment as switchActiveSubscriptionWithNewPaymentAction
} from "./subscription/manage/switch";

// Import subscription status and lifecycle actions
import {
  pauseUserSubscription,
  activateUserSubscription,
  getSubscriptionDetails as getSubscriptionDetailsAction
} from "./subscription/status";

// Import payment-related actions
import {
  createSubscriptionPayment as createSubscriptionPaymentAction,
  createSubscriptionAuth as createSubscriptionAuthAction,
  createSubscriptionCharge as createSubscriptionChargeAction,
  getPaymentDetails as getPaymentDetailsAction,
  managePayment as managePaymentAction,
  retryPayment as retryPaymentAction,
  cancelPayment as cancelPaymentAction
} from "./subscription/payment";

// Refund-related actions have been removed

// Import trial activation action
import {
  activateTrialForFirstTimePaidSubscriber as activateTrialForFirstTimePaidSubscriberAction
} from "./subscription/activateTrial";

// Re-export all actions with their original names
export const createSubscription = createSubscriptionAction;
export const cancelAndCreateSubscription = cancelAndCreateSubscriptionAction;

export const cancelSubscription = cancelSubscriptionAction;
export const changePlan = changePlanAction;
export const changePlanWithManage = changePlanWithManageAction;
export const scheduleSubscriptionChange = scheduleSubscriptionChangeAction;
export const manageSubscription = manageSubscriptionAction;
export const switchAuthenticatedSubscription = switchAuthenticatedSubscriptionAction;
export const switchActiveSubscription = switchActiveSubscriptionAction;
export const switchActiveSubscriptionWithNewPayment = switchActiveSubscriptionWithNewPaymentAction;

export const pauseSubscription = pauseUserSubscription;
export const activateSubscription = activateUserSubscription;
export const getSubscriptionDetails = getSubscriptionDetailsAction;


export const createSubscriptionPayment = createSubscriptionPaymentAction;
export const createSubscriptionAuth = createSubscriptionAuthAction;
export const createSubscriptionCharge = createSubscriptionChargeAction;
export const getPaymentDetails = getPaymentDetailsAction;
export const managePayment = managePaymentAction;
export const retryPayment = retryPaymentAction;
export const cancelPayment = cancelPaymentAction;

// Refund exports have been removed

export const activateTrialForFirstTimePaidSubscriber = activateTrialForFirstTimePaidSubscriberAction;
