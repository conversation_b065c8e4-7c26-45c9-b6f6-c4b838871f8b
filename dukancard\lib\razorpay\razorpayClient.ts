/**
 * Razorpay Client
 *
 * This file exports all Razorpay-related functions and utilities.
 */

// Re-export all functions from the utility files
export {
  RAZORPAY_API_URL,
  getRazorpayAuth,
  getRazorpayApiHeaders,
  verifyWebhookSignature,
  verifyRazorpayCredentials,
} from "./utils/auth";

// Re-export all functions from the webhook service
export {
  createWebhook,
  fetchWebhooks,
  fetchWebhook,
  updateWebhook,
  deleteWebhook,
} from "./services/webhook";

// Re-export all functions from the plan service
export {
  getPlan,
} from "./services/plan";

// Re-export all functions from the subscription service
export {
  createSubscription,
  getSubscription,
  cancelSubscription,
  updateSubscription,
  pauseSubscription,
  resumeSubscription,
  listSubscriptions,
  getScheduledChanges,
  cancelScheduledChanges,
} from "./services/subscription";

// Re-export all functions from the invoice service
export {
  getInvoicesForSubscription,
} from "./services/invoice";

// Re-export webhook types
export type {
  CreateWebhookParams,
  FetchWebhooksParams,
  UpdateWebhookParams,
  RazorpayWebhook,
} from "./services/webhook";

// Re-export plan types
export type {
  RazorpayPlan,
  RazorpayPlanItem,
} from "./services/plan";

// Re-export subscription types
export type {
  CreateSubscriptionParams,
  UpdateSubscriptionParams,
  CancelSubscriptionParams,
  PauseSubscriptionParams,
  ResumeSubscriptionParams,
  RazorpayApiResponse,
} from "./services/subscription/types";

// Re-export invoice types
export type {
  RazorpayInvoice,
  RazorpayInvoicesCollection,
} from "./services/invoice";

// Re-export payment types
export type {
  RefundPaymentParams,
  RazorpayRefund,
  RefundPaymentResponse,
  RazorpayPayment,
} from "./services/payment";

// Re-export all functions from the customer service
export {
  createCustomer,
  getCustomer,
  updateCustomer,
  findCustomerByEmail,
  listCustomers,
} from "./services/customer";

// Re-export customer types
export type {
  RazorpayCustomer,
  CreateCustomerParams,
  UpdateCustomerParams,
  FetchCustomersParams,
  CustomerApiResponse,
  CustomersListApiResponse,
} from "./services/customer";

// Re-export all functions from the payment service
export {
  getPayment,
} from "./services/payment";

// TODO: Implement other payment service functions
// export {
//   createPayment,
//   capturePayment,
// } from "./services/payment";
