'use client';


import { FeedFilterType } from '@/lib/types/posts';
import FilterPills from './FilterPills';

interface ModernFeedHeaderProps {
  activeFilter: FeedFilterType;
  onFilterChange: (_filter: FeedFilterType) => void;
  isLoading?: boolean;
}

export default function ModernFeedHeader({
  activeFilter,
  onFilterChange,
  isLoading = false
}: ModernFeedHeaderProps) {
  return (
    <div className="mb-8">
      {/* Filter Section */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-foreground">
          Your Feed
        </h2>
        <div className="text-sm text-muted-foreground">
          Choose your feed preference
        </div>
      </div>

      <FilterPills
        activeFilter={activeFilter}
        onFilterChange={onFilterChange}
        isLoading={isLoading}
      />
    </div>
  );
}
