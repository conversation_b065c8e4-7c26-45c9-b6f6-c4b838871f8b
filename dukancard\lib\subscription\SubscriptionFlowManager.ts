/**
 * CENTRALIZED SUBSCRIPTION FLOW MANAGER
 * 
 * This is the single source of truth for all subscription logic.
 * It implements professional enterprise-level logic for handling:
 * - Fresh subscriptions (post-trial, no active subscription)
 * - Plan switches (active subscription with different payment methods)
 * - Trial to paid transitions
 * 
 * BUSINESS RULES (SIMPLIFIED):
 * 1. Post-trial + no razorpay_subscription_id = UPFRONT_PAYMENT (take money first, update DB after active)
 * 2. Active subscription + any payment method = CREATE_AND_CANCEL (create new, cancel old after active)
 * 3. Fresh subscription (trial or no subscription) = FRESH_SUBSCRIPTION
 */

import {
  SubscriptionRequest,
  SubscriptionFlowDecision,
  PLAN_IDS
} from './types';

export class SubscriptionFlowManager {
  /**
   * MASTER DECISION FUNCTION
   * Determines the correct subscription flow based on current state
   */
  static determineSubscriptionFlow(request: SubscriptionRequest): SubscriptionFlowDecision {
    const { context } = request;
    const {
      trialEndDate,
      razorpaySubscriptionId
    } = context;

    // Check if user is post-trial
    const isPostTrial = this.isPostTrial(trialEndDate);
    
    // Check if user has active Razorpay subscription
    const hasRazorpaySubscription = Boolean(razorpaySubscriptionId);

    // BUSINESS RULE 1: Post-trial with no active subscription
    if (isPostTrial && !hasRazorpaySubscription) {
      return {
        flowType: 'UPFRONT_PAYMENT',
        requiresUpfrontPayment: true,
        shouldCancelExisting: false,
        shouldUpdateExisting: false,
        paymentTiming: 'IMMEDIATE',
        reason: 'Post-trial user with no active subscription - requires upfront payment'
      };
    }

    // BUSINESS RULE 2 & 3: Active subscription with any payment method
    // Simplified: Always use create/cancel flow for all payment methods
    if (hasRazorpaySubscription) {
      return {
        flowType: 'CREATE_AND_CANCEL',
        requiresUpfrontPayment: true,
        shouldCancelExisting: true,
        shouldUpdateExisting: false,
        paymentTiming: 'IMMEDIATE',
        reason: 'Active subscription - create new and cancel old (simplified flow for all payment methods)'
      };
    }

    // BUSINESS RULE 4: Fresh subscription (trial or no subscription)
    return {
      flowType: 'FRESH_SUBSCRIPTION',
      requiresUpfrontPayment: !this.isOnTrial(trialEndDate),
      shouldCancelExisting: false,
      shouldUpdateExisting: false,
      paymentTiming: this.isOnTrial(trialEndDate) ? 'TRIAL_END' : 'IMMEDIATE',
      reason: 'Fresh subscription - new user or trial user'
    };
  }

  /**
   * Check if user is post-trial (trial has ended)
   */
  private static isPostTrial(trialEndDate?: string | null): boolean {
    if (!trialEndDate) return false;
    return new Date(trialEndDate) <= new Date();
  }

  /**
   * Check if user is currently on trial
   */
  private static isOnTrial(trialEndDate?: string | null): boolean {
    if (!trialEndDate) return false;
    return new Date(trialEndDate) > new Date();
  }



  /**
   * Validate subscription request
   */
  static validateRequest(request: SubscriptionRequest): { valid: boolean; error?: string } {
    const { planId, planCycle, context } = request;

    // Validate plan ID
    if (!Object.values(PLAN_IDS).includes(planId as 'free' | 'basic' | 'growth' | 'pro' | 'enterprise')) {
      return { valid: false, error: 'Invalid plan ID' };
    }

    // Validate plan cycle
    if (!['monthly', 'yearly'].includes(planCycle)) {
      return { valid: false, error: 'Invalid plan cycle' };
    }

    // Validate context
    if (!context.userId) {
      return { valid: false, error: 'User ID is required' };
    }

    return { valid: true };
  }

  /**
   * Get user-friendly flow description
   */
  static getFlowDescription(decision: SubscriptionFlowDecision): string {
    switch (decision.flowType) {
      case 'UPFRONT_PAYMENT':
        return 'Payment will be processed immediately and subscription activated upon successful payment.';

      case 'CREATE_AND_CANCEL':
        return 'A new subscription will be created and your current subscription will be cancelled after activation.';
      case 'FRESH_SUBSCRIPTION':
        return decision.paymentTiming === 'TRIAL_END'
          ? 'Subscription will be created and payment will be processed when your trial ends.'
          : 'New subscription will be created and activated immediately.';
      default:
        return 'Subscription will be processed according to your current plan status.';
    }
  }
}
