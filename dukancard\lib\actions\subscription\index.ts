"use server";

// Import all actions and re-export them individually
// This approach is needed because "use server" files can only export async functions directly

// Import subscription creation actions
import { createSubscription, cancelAndCreateSubscription } from "./create";

// Import subscription management actions
import {
  cancelSubscription,
  changePlan,
  changePlanWithManage,
  scheduleSubscriptionChange,
  manageSubscription,
  switchAuthenticatedSubscription
} from "./manage";

// Import switch functions directly from switch.ts
import { switchActiveSubscription, switchActiveSubscriptionWithNewPayment } from "./manage/switch";

// Refund policy functions have been removed

// Import subscription status and lifecycle actions
import {
  pauseUserSubscription,
  activateUserSubscription,
  getSubscriptionDetails
} from "./status";

// Import payment-related actions
import {
  createSubscriptionPayment,
  createSubscriptionAuth,
  createSubscriptionCharge,
  getPaymentDetails,
  managePayment,
  retryPayment,
  cancelPayment
} from "./payment";

// Refund-related actions have been removed

// Import trial activation action
import { activateTrialForFirstTimePaidSubscriber } from "./activateTrial";



// Re-export all actions
export { createSubscription, cancelAndCreateSubscription };
export {
  cancelSubscription,
  changePlan,
  changePlanWithManage,
  scheduleSubscriptionChange,
  manageSubscription,
  switchAuthenticatedSubscription,
  switchActiveSubscription,
  switchActiveSubscriptionWithNewPayment
};
export {
  pauseUserSubscription as pauseSubscription,
  activateUserSubscription as activateSubscription,
  getSubscriptionDetails
};
export {
  createSubscriptionPayment,
  createSubscriptionAuth,
  createSubscriptionCharge,
  getPaymentDetails,
  managePayment,
  retryPayment,
  cancelPayment
};
// Refund exports have been removed
export { activateTrialForFirstTimePaidSubscriber };

