/**
 * Razorpay Subscription Types
 *
 * This file contains types for Razorpay subscription API requests and responses.
 */

import { RazorpaySubscription } from "../../types/api";

// Razorpay API response type
export interface RazorpayApiResponse<T> {
  success: boolean;
  data?: T;
  error?: unknown;
}

// Addon item interface
export interface AddonItem {
  name: string;
  amount: number;
  currency: string;
}

// Addon interface
export interface Addon {
  item: AddonItem;
}

// Create subscription parameters
export interface CreateSubscriptionParams {
  plan_id: string;
  total_count: number;
  quantity?: number;
  start_at?: number;
  expire_by?: number;
  customer_notify?: boolean;
  addons?: Addon[];
  offer_id?: string;
  notes?: Record<string, string>;
}

// Subscription response type (re-exported for convenience)
export type { RazorpaySubscription };

// Cancel subscription parameters
export interface CancelSubscriptionParams {
  cancel_at_cycle_end?: 0 | 1;
}

// Pause subscription parameters
export interface PauseSubscriptionParams {
  pause_at?: string; // "now" or "cycle_end" - not required for authenticated subscriptions
}

// Resume subscription parameters
export interface ResumeSubscriptionParams {
  resume_at: string; // "now"
}

// Update subscription parameters
export interface UpdateSubscriptionParams {
  plan_id?: string;
  quantity?: number;
  remaining_count?: number;
  schedule_change_at?: string; // "now" or "cycle_end"
  customer_notify?: boolean;
  addons?: Addon[];
  notes?: Record<string, string>;
}
