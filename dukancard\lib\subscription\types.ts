// Subscription status constants
export const SUBSCRIPTION_STATUS = {
  TRIAL: 'trial',
  PENDING: 'pending',
  AUTHENTICATED: 'authenticated',
  ACTIVE: 'active',
  HALTED: 'halted',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
  EXPIRED: 'expired',
  PAUSED: 'paused'
} as const;

// Plan IDs
export const PLAN_IDS = {
  FREE: 'free',
  BASIC: 'basic',
  GROWTH: 'growth',
  PRO: 'pro',
  ENTERPRISE: 'enterprise'
} as const;

export type SubscriptionStatus = typeof SUBSCRIPTION_STATUS[keyof typeof SUBSCRIPTION_STATUS];
export type PlanId = typeof PLAN_IDS[keyof typeof PLAN_IDS];
export type PlanCycle = 'monthly' | 'yearly';

// Centralized subscription flow types
export interface SubscriptionContext {
  userId: string;
  currentPlanId?: string;
  currentPlanCycle?: PlanCycle;
  subscriptionStatus?: string;
  trialEndDate?: string | null;
  razorpaySubscriptionId?: string | null;
  lastPaymentMethod?: string | null;
  hasActiveSubscription?: boolean;
}

export interface SubscriptionRequest {
  planId: PlanId;
  planCycle: PlanCycle;
  context: SubscriptionContext;
}

export interface SubscriptionFlowDecision {
  flowType: 'FRESH_SUBSCRIPTION' | 'CREATE_AND_CANCEL' | 'UPFRONT_PAYMENT';
  requiresUpfrontPayment: boolean;
  shouldCancelExisting: boolean;
  shouldUpdateExisting: boolean;
  paymentTiming: 'IMMEDIATE' | 'TRIAL_END';
  reason: string;
}

export interface SubscriptionFlowResult {
  success: boolean;
  subscriptionId?: string;
  paymentRequired: boolean;
  message: string;
  error?: string;
  data?: Record<string, unknown>;
}

// Business profile and payment subscription interfaces
export interface BusinessProfile {
  id: string;
  trial_end_date?: string | null;
  has_active_subscription?: boolean;
  status?: string;
}

export interface PaymentSubscription {
  id: string;
  business_profile_id: string;
  razorpay_subscription_id?: string | null;
  subscription_status: string;
  plan_id: string;
  plan_cycle: string;
  last_payment_method?: string | null;
  subscription_start_date?: string | null;
  subscription_expiry_time?: string | null;
  cancelled_at?: string | null;
  created_at: string;
  updated_at: string;
}
