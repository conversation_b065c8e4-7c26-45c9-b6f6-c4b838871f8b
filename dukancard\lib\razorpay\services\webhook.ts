/**
 * Razorpay Webhook Service
 *
 * This file contains functions for managing Razorpay webhooks:
 * - Creating webhooks
 * - Fetching webhooks
 * - Updating webhooks
 * - Deleting webhooks
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../utils/auth";

// Webhook creation parameters
export interface CreateWebhookParams {
  url: string;
  alert_email?: string;
  secret?: string;
  events: string[];
}

// Webhook response interface
export interface RazorpayWebhook {
  id: string;                // The unique identifier of the webhook
  created_at: number;        // The Unix timestamp at which the webhook has been created
  updated_at: number;        // The Unix timestamp at which the webhook has been updated
  service?: string;          // The service that created the webhook
  owner_id: string;          // The unique identifier of the sub-merchant who will receive the webhooks
  owner_type: string;        // Indicates the type of owner (e.g., "merchant")
  context?: unknown[];       // Additional context information
  disabled_at?: number;      // The Unix timestamp at which the webhook was disabled (0 if active)
  url: string;               // The URL where webhook payloads are sent
  alert_email?: string;      // The email address for webhook failure notifications
  secret?: string;           // The webhook secret (only included in create response)
  secret_exists: boolean;    // Whether a secret has been set for the webhook
  entity: string;            // The type of entity (always "webhook")
  active: boolean;           // Whether the webhook is active (true) or inactive (false)
  events: string[];          // The events the webhook is subscribed to
}

// API response interface
export interface RazorpayApiResponse<T> {
  success: boolean;
  data?: T;
  error?: unknown;
}

/**
 * Create a webhook in Razorpay
 *
 * @param accountId The account ID for which to create the webhook
 * @param params The webhook parameters
 * @returns The created webhook or error
 *
 * Example usage:
 * ```
 * const result = await createWebhook('acc_123456789', {
 *   url: 'https://example.com/webhooks/razorpay',
 *   alert_email: '<EMAIL>',
 *   secret: 'webhook_secret',
 *   events: [
 *     'payment.authorized',
 *     'payment.failed',
 *     'payment.captured',
 *     'subscription.charged',
 *     'subscription.cancelled'
 *   ]
 * });
 * ```
 */
export async function createWebhook(
  accountId: string,
  params: CreateWebhookParams
): Promise<RazorpayApiResponse<RazorpayWebhook>> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL}/accounts/${accountId}/webhooks`, {
      method: 'POST',
      headers,
      body: JSON.stringify(params)
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY] Error creating webhook:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY] Exception creating webhook:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Query parameters for fetching webhooks
 */
export interface FetchWebhooksParams {
  from?: number;    // Timestamp, in seconds, from when the webhooks are to be fetched
  to?: number;      // Timestamp, in seconds, till when the webhooks are to be fetched
  count?: number;   // Number of webhooks to be fetched (default: 10, max: 100)
  skip?: number;    // Number of records to be skipped for pagination
}

/**
 * Fetch all webhooks for an account
 *
 * @param accountId The account ID for which to fetch webhooks
 * @param params Optional query parameters (from, to, count, skip)
 * @returns The list of webhooks or error
 *
 * Example usage:
 * ```
 * // Fetch all webhooks
 * const result = await fetchWebhooks('acc_123456789');
 *
 * // Fetch webhooks with pagination
 * const result = await fetchWebhooks('acc_123456789', { count: 20, skip: 10 });
 *
 * // Fetch webhooks within a date range
 * const result = await fetchWebhooks('acc_123456789', {
 *   from: Math.floor(new Date('2023-01-01').getTime() / 1000),
 *   to: Math.floor(new Date('2023-12-31').getTime() / 1000)
 * });
 * ```
 */
export async function fetchWebhooks(
  accountId: string,
  params?: FetchWebhooksParams
): Promise<RazorpayApiResponse<RazorpayWebhook[]>> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Build query string from params
    let queryString = '';
    if (params) {
      const queryParams = new URLSearchParams();

      if (params.from !== undefined) {
        queryParams.append('from', params.from.toString());
      }

      if (params.to !== undefined) {
        queryParams.append('to', params.to.toString());
      }

      if (params.count !== undefined) {
        queryParams.append('count', params.count.toString());
      }

      if (params.skip !== undefined) {
        queryParams.append('skip', params.skip.toString());
      }

      const queryParamsString = queryParams.toString();
      if (queryParamsString) {
        queryString = `?${queryParamsString}`;
      }
    }

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL}/accounts/${accountId}/webhooks${queryString}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY] Error fetching webhooks:', data);
      return { success: false, error: data };
    }


    return { success: true, data: data.items };
  } catch (error) {
    console.error('[RAZORPAY] Exception fetching webhooks:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Fetch a specific webhook by ID
 *
 * @param accountId The account ID
 * @param webhookId The webhook ID to fetch
 * @returns The webhook details or error
 */
export async function fetchWebhook(
  accountId: string,
  webhookId: string
): Promise<RazorpayApiResponse<RazorpayWebhook>> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL}/accounts/${accountId}/webhooks/${webhookId}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY] Error fetching webhook:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY] Exception fetching webhook:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update webhook parameters
 */
export interface UpdateWebhookParams {
  url: string;      // The URL where webhook payloads are sent (required)
  events: string[]; // The events the webhook is subscribed to (required)
}

/**
 * Update a webhook in Razorpay
 *
 * @param accountId The account ID
 * @param webhookId The webhook ID to update
 * @param params The updated webhook parameters (url and events are required)
 * @returns The updated webhook or error
 *
 * Example usage:
 * ```
 * const result = await updateWebhook('acc_123456789', 'HK890egfiItP3H', {
 *   url: 'https://www.example.com/webhooks/razorpay',
 *   events: ['payment.authorized', 'payment.captured', 'refund.created']
 * });
 * ```
 */
export async function updateWebhook(
  accountId: string,
  webhookId: string,
  params: UpdateWebhookParams
): Promise<RazorpayApiResponse<RazorpayWebhook>> {
  try {

    // Validate required parameters
    if (!params.url) {
      return {
        success: false,
        error: 'URL is required for updating a webhook'
      };
    }

    if (!params.events || !Array.isArray(params.events) || params.events.length === 0) {
      return {
        success: false,
        error: 'Events array is required for updating a webhook'
      };
    }

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL}/accounts/${accountId}/webhooks/${webhookId}`, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(params)
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY] Error updating webhook:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY] Exception updating webhook:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete a webhook from Razorpay
 *
 * @param accountId The account ID
 * @param webhookId The webhook ID to delete
 * @returns Success or error
 *
 * Example usage:
 * ```
 * const result = await deleteWebhook('acc_H3kYHQ635sBwXG', 'HK890egfiItP3H');
 * ```
 */
export async function deleteWebhook(
  accountId: string,
  webhookId: string
): Promise<RazorpayApiResponse<null>> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL}/accounts/${accountId}/webhooks/${webhookId}`, {
      method: 'DELETE',
      headers
    });

    if (!response.ok) {
      let errorData;
      try {
        // Try to parse error response as JSON
        errorData = await response.json();
      } catch (_) {
        // If parsing fails, use the status text
        errorData = { error: response.statusText };
      }

      console.error('[RAZORPAY] Error deleting webhook:', errorData);
      return { success: false, error: errorData };
    }

    // According to the Razorpay API documentation, the response is an empty array
    // We don't need to parse it, just return success
    return { success: true };
  } catch (error) {
    console.error('[RAZORPAY] Exception deleting webhook:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
