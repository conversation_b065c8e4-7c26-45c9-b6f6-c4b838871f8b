/**
 * Razorpay Payment Retrieval
 *
 * This file contains functions for fetching Razorpay payment details.
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { RazorpayApiResponse, RazorpayPayment } from "./types";

/**
 * Get a payment from Razorpay by ID
 *
 * API Endpoint: GET https://api.razorpay.com/v1/payments/:id
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param paymentId The payment ID
 * @returns The payment details or error
 * 
 * Example response for Netbanking:
 * ```json
 * {
 *   "id": "pay_MT48CvBhIC98MQ",
 *   "entity": "payment",
 *   "amount": 2100,
 *   "currency": "INR",
 *   "status": "captured",
 *   "order_id": "order_MT47xgV5ApouIB",
 *   "invoice_id": null,
 *   "international": false,
 *   "method": "netbanking",
 *   "amount_refunded": 0,
 *   "refund_status": null,
 *   "captured": true,
 *   "description": "#MT47qgzX2EOko2",
 *   "card_id": null,
 *   "bank": "ICIC",
 *   "wallet": null,
 *   "vpa": null,
 *   "email": "<EMAIL>",
 *   "contact": "+************",
 *   "notes": [],
 *   "fee": 50,
 *   "tax": 8,
 *   "error_code": null,
 *   "error_description": null,
 *   "error_source": null,
 *   "error_step": null,
 *   "error_reason": null,
 *   "acquirer_data": {
 *       "bank_transaction_id": "6951370"
 *   },
 *   "created_at": **********
 * }
 * ```
 * 
 * Example response for UPI:
 * ```json
 * {
 *   "id": "pay_MT3PgKGazkDUUM",
 *   "entity": "payment",
 *   "amount": 1000,
 *   "currency": "INR",
 *   "status": "captured",
 *   "order_id": "order_MLXS6VLbhKH7i1",
 *   "invoice_id": "inv_MLXS5jI0oFNX2a",
 *   "international": false,
 *   "method": "upi",
 *   "amount_refunded": 0,
 *   "refund_status": null,
 *   "captured": true,
 *   "description": "#MLXS5jI0oFNX2a",
 *   "card_id": null,
 *   "bank": null,
 *   "wallet": null,
 *   "vpa": "gaurav.kumar@examplebank",
 *   "email": null,
 *   "contact": "+************",
 *   "notes": [],
 *   "fee": 24,
 *   "tax": 4,
 *   "error_code": null,
 *   "error_description": null,
 *   "error_source": null,
 *   "error_step": null,
 *   "error_reason": null,
 *   "acquirer_data": {
 *       "rrn": "************"
 *   },
 *   "created_at": **********,
 *   "upi": {
 *       "payer_account_type": "credit_card",
 *       "vpa": "gaurav.kumar@examplebank",
 *       "flow": "intent"
 *   }
 * }
 * ```
 * 
 * Example response for Card:
 * ```json
 * {
 *   "id": "pay_DG4ZdRK8ZnXC3k",
 *   "entity": "payment",
 *   "amount": 100,
 *   "currency": "INR",
 *   "status": "captured",
 *   "order_id": "order_GjCr5oKh4AVC51",
 *   "invoice_id": null,
 *   "international": false,
 *   "method": "card",
 *   "amount_refunded": 0,
 *   "refund_status": null,
 *   "captured": true,
 *   "description": "Payment for Adidas shoes",
 *   "card_id": "card_KOdY30ajbuyOYN",
 *   "bank": null,
 *   "wallet": null,
 *   "vpa": null,
 *   "email": "<EMAIL>",
 *   "contact": "**********",
 *   "customer_id": "cust_K6fNE0WJZWGqtN",
 *   "token_id": "token_KOdY$DBYQOv08n",
 *   "notes": [],
 *   "fee": 1,
 *   "tax": 0,
 *   "error_code": null,
 *   "error_description": null,
 *   "error_source": null,
 *   "error_step": null,
 *   "error_reason": null,
 *   "acquirer_data": {
 *       "auth_code": "064381",
 *       "arn": "74119663031031075351326",
 *       "rrn": "************"
 *   },
 *   "created_at": **********
 * }
 * ```
 * 
 * Example response for Wallet:
 * ```json
 * {
 *   "id": "pay_MT4GRFUHBfMCNf",
 *   "entity": "payment",
 *   "amount": 250000,
 *   "currency": "INR",
 *   "status": "captured",
 *   "order_id": "order_MT4FE4i64KutqB",
 *   "invoice_id": null,
 *   "international": false,
 *   "method": "wallet",
 *   "amount_refunded": 0,
 *   "refund_status": null,
 *   "captured": true,
 *   "description": "#MT4F1XRgHKcWEt",
 *   "card_id": null,
 *   "bank": null,
 *   "wallet": "airtelmoney",
 *   "vpa": null,
 *   "email": "<EMAIL>",
 *   "contact": "+************",
 *   "notes": [],
 *   "fee": 5900,
 *   "tax": 900,
 *   "error_code": null,
 *   "error_description": null,
 *   "error_source": null,
 *   "error_step": null,
 *   "error_reason": null,
 *   "acquirer_data": {
 *       "transaction_id": null
 *   },
 *   "created_at": **********
 * }
 * ```
 */
export async function getPayment(
  paymentId: string
): Promise<RazorpayApiResponse<RazorpayPayment>> {
  try {


    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/payments/${paymentId}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching payment:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching payment:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
