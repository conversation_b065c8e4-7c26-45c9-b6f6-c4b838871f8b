/**
 * Razorpay Subscription Retrieval
 *
 * This file contains functions for fetching Razorpay subscription details.
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { RazorpayApiResponse, RazorpaySubscription } from "./types";

/**
 * Get a subscription from Razorpay
 *
 * API Endpoint: GET https://api.razorpay.com/v1/subscriptions/:id
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param subscriptionId The subscription ID
 * @returns The subscription details or error
 */
export async function getSubscription(
  subscriptionId: string
): Promise<RazorpayApiResponse<RazorpaySubscription>> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching subscription:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching subscription:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}

/**
 * List all subscriptions from Razorpay
 *
 * API Endpoint: GET https://api.razorpay.com/v1/subscriptions
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param params Optional query parameters (count, skip, etc.)
 * @returns List of subscriptions or error
 */
export async function listSubscriptions(
  params: Record<string, string | number> = {}
): Promise<RazorpayApiResponse<{ entity: string; count: number; items: RazorpaySubscription[] }>> {
  try {

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Build query string
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    // Make API request
    const url = `${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions${queryString ? `?${queryString}` : ''}`;
    const response = await fetch(url, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching subscriptions:', data);
      return { success: false, error: data };
    }


    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching subscriptions:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
