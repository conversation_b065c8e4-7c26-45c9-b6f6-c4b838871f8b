/**
 * Razorpay Invoice Service
 *
 * This file contains functions for managing Razorpay invoices:
 * - Fetching invoices for a subscription
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../utils/auth";
import { RazorpayApiResponse } from "./subscription/types";

// Razorpay invoice interface
export interface RazorpayInvoice {
  id: string;
  entity: string;
  receipt?: string | null;
  invoice_number?: string | null;
  customer_id: string;
  customer_details: {
    id: string;
    name?: string | null;
    email: string;
    contact: string;
    gstin?: string | null;
    billing_address?: unknown | null;
    shipping_address?: unknown | null;
    customer_name?: string | null;
    customer_email: string;
    customer_contact: string;
  };
  order_id: string;
  subscription_id?: string;
  line_items: Array<{
    id: string;
    item_id?: string | null;
    ref_id?: string | null;
    ref_type?: string | null;
    name: string;
    description?: string | null;
    amount: number;
    unit_amount: number;
    gross_amount: number;
    tax_amount: number;
    taxable_amount: number;
    net_amount: number;
    currency: string;
    type: string;
    tax_inclusive: boolean;
    hsn_code?: string | null;
    sac_code?: string | null;
    tax_rate?: number | null;
    unit?: string | null;
    quantity: number;
    taxes: unknown[];
  }>;
  payment_id?: string;
  status: string;
  expire_by?: number | null;
  issued_at: number;
  paid_at?: number | null;
  cancelled_at?: number | null;
  expired_at?: number | null;
  sms_status?: string | null;
  email_status?: string | null;
  date: number;
  terms?: string | null;
  partial_payment: boolean;
  gross_amount: number;
  tax_amount: number;
  taxable_amount: number;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  currency_symbol: string;
  description?: string | null;
  notes: Record<string, string>;
  comment?: string | null;
  short_url: string;
  view_less: boolean;
  billing_start?: number;
  billing_end?: number;
  type: string;
  group_taxes_discounts: boolean;
  created_at: number;
  idempotency_key?: string | null;
}

// Razorpay invoices collection interface
export interface RazorpayInvoicesCollection {
  entity: string;
  count: number;
  items: RazorpayInvoice[];
}

/**
 * Get all invoices for a subscription from Razorpay
 *
 * API Endpoint: GET https://api.razorpay.com/v1/invoices?subscription_id=:sub_id
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param subscriptionId The subscription ID
 * @param page The page number (1-based)
 * @param count The number of items per page
 * @returns The invoices for the subscription or error
 *
 * Example response:
 * ```json
 * {
 *   "entity": "collection",
 *   "count": 2,
 *   "items": [
 *     {
 *       "id": "inv_00000000000003",
 *       "entity": "invoice",
 *       "receipt": null,
 *       "invoice_number": null,
 *       "customer_id": "cust_00000000000001",
 *       "customer_details": {
 *         "id": "cust_00000000000001",
 *         "name": null,
 *         "email": "<EMAIL>",
 *         "contact": "+919876543210",
 *         "gstin": null,
 *         "billing_address": null,
 *         "shipping_address": null,
 *         "customer_name": null,
 *         "customer_email": "<EMAIL>",
 *         "customer_contact": "+919876543210"
 *       },
 *       "order_id": "order_00000000000002",
 *       "subscription_id": "sub_00000000000001",
 *       "line_items": [
 *         {
 *           "id": "li_00000000000003",
 *           "item_id": null,
 *           "ref_id": null,
 *           "ref_type": null,
 *           "name": "Monthly Plan",
 *           "description": null,
 *           "amount": 99900,
 *           "unit_amount": 99900,
 *           "gross_amount": 99900,
 *           "tax_amount": 0,
 *           "taxable_amount": 99900,
 *           "net_amount": 99900,
 *           "currency": "INR",
 *           "type": "plan",
 *           "tax_inclusive": false,
 *           "hsn_code": null,
 *           "sac_code": null,
 *           "tax_rate": null,
 *           "unit": null,
 *           "quantity": 1,
 *           "taxes": []
 *         }
 *       ],
 *       "payment_id": "pay_00000000000002",
 *       "status": "paid",
 *       "expire_by": null,
 *       "issued_at": 1593344888,
 *       "paid_at": 1593344889,
 *       "cancelled_at": null,
 *       "expired_at": null,
 *       "sms_status": null,
 *       "email_status": null,
 *       "date": 1593344888,
 *       "terms": null,
 *       "partial_payment": false,
 *       "gross_amount": 99900,
 *       "tax_amount": 0,
 *       "taxable_amount": 99900,
 *       "amount": 99900,
 *       "amount_paid": 99900,
 *       "amount_due": 0,
 *       "currency": "INR",
 *       "currency_symbol": "₹",
 *       "description": null,
 *       "notes": [],
 *       "comment": null,
 *       "short_url": "https://rzp.io/i/Ys4feGqEp",
 *       "view_less": true,
 *       "billing_start": 1594405800,
 *       "billing_end": 1597084200,
 *       "type": "invoice",
 *       "group_taxes_discounts": false,
 *       "created_at": 1593344888,
 *       "idempotency_key": null
 *     }
 *   ]
 * }
 * ```
 */
export async function getInvoicesForSubscription(
  subscriptionId: string,
  page: number = 1,
  count: number = 10
): Promise<RazorpayApiResponse<RazorpayInvoicesCollection>> {
  try {
    console.log(`[RAZORPAY_DEBUG] Fetching invoices for subscription: ${subscriptionId}, page: ${page}, count: ${count}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Calculate skip value for pagination
    const skip = (page - 1) * count;

    // Make API request with pagination parameters
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/invoices?subscription_id=${subscriptionId}&count=${count}&skip=${skip}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching invoices:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully fetched ${data.count} invoices for subscription ${subscriptionId}`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching invoices:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
