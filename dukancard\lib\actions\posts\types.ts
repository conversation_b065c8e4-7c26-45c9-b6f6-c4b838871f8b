/**
 * Internal types for posts actions
 */

import { PostWithBusinessProfile } from '@/lib/types/posts';
import type { SupabaseClient as BaseSupabaseClient } from '@supabase/supabase-js';
import type { User } from '@supabase/supabase-js';

// Type for posts with feed scoring
export type PostWithScore = PostWithBusinessProfile & { feed_score: number };

// Type for user profile data
export interface UserProfile {
  locality_slug?: string | null;
  city_slug?: string | null;
  state_slug?: string | null;
  pincode?: string | null;
}

// Type for subscription data
export interface SubscriptionData {
  business_profile_id: string;
}

// Type for Supabase client (using proper Supabase type)
export type SupabaseClient = BaseSupabaseClient;

// Type for authenticated user (using proper Supabase User type)
export type AuthenticatedUser = User;
