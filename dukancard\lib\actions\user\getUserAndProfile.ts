"use server";

import { getUserAndProfile as originalGetUserAndProfile } from "@/lib/actions/subscription/utils";

// Re-export the types
export type { UserType, BusinessProfileType } from "@/lib/actions/subscription/utils";

/**
 * Wrapper function for getUserAndProfile from subscription utils
 * This creates a proper async function export for the "use server" directive
 *
 * @param select The fields to select from the subscription
 * @returns An object with user, profile, subscription, or an error response
 */
export async function getUserAndProfile(select: string = "*") {
  return await originalGetUserAndProfile(select);
}
