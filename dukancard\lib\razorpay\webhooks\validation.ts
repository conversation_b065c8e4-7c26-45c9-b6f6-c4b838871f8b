/**
 * Enhanced webhook validation utilities
 * Provides comprehensive validation for Razorpay webhooks
 */

import { RazorpayWebhookData } from "../types/api";

export interface WebhookValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validate webhook timestamp to prevent replay attacks
 * Razorpay webhooks should be processed within a reasonable time window
 */
export function validateWebhookTimestamp(
  timestamp: number,
  maxAgeMinutes: number = 5
): { valid: boolean; error?: string } {
  const now = Math.floor(Date.now() / 1000);
  const maxAge = maxAgeMinutes * 60; // Convert to seconds
  
  if (timestamp > now + 60) { // Allow 1 minute clock skew
    return { 
      valid: false, 
      error: `Webhook timestamp is in the future: ${timestamp} > ${now}` 
    };
  }
  
  if (now - timestamp > maxAge) {
    return { 
      valid: false, 
      error: `Webhook timestamp is too old: ${now - timestamp} seconds > ${maxAge} seconds` 
    };
  }
  
  return { valid: true };
}

/**
 * Validate webhook payload structure
 */
export function validateWebhookPayload(payload: unknown): WebhookValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check if payload is an object
  if (!payload || typeof payload !== 'object') {
    errors.push('Payload must be an object');
    return { valid: false, errors, warnings };
  }
  
  const webhookData = payload as Record<string, unknown>;
  
  // Required fields
  if (!webhookData.event || typeof webhookData.event !== 'string') {
    errors.push('Missing or invalid event type');
  }
  
  if (!webhookData.account_id || typeof webhookData.account_id !== 'string') {
    errors.push('Missing or invalid account_id');
  }
  
  if (!webhookData.entity || typeof webhookData.entity !== 'string') {
    errors.push('Missing or invalid entity type');
  }
  
  if (!webhookData.payload || typeof webhookData.payload !== 'object') {
    errors.push('Missing or invalid payload object');
  }
  
  // Validate created_at timestamp if present
  if (webhookData.created_at) {
    if (typeof webhookData.created_at !== 'number') {
      errors.push('created_at must be a number (Unix timestamp)');
    } else {
      const timestampValidation = validateWebhookTimestamp(webhookData.created_at);
      if (!timestampValidation.valid) {
        warnings.push(`Timestamp validation: ${timestampValidation.error}`);
      }
    }
  }
  
  // Validate subscription events specifically
  if (typeof webhookData.event === 'string' && webhookData.event.startsWith('subscription.')) {
    const payloadObj = webhookData.payload as Record<string, unknown>;
    
    if (!payloadObj.subscription) {
      errors.push('Subscription events must contain subscription data');
    } else {
      const subscription = payloadObj.subscription as Record<string, unknown>;
      
      if (!subscription.entity || typeof subscription.entity !== 'object') {
        errors.push('Subscription data must contain entity object');
      } else {
        const entity = subscription.entity as Record<string, unknown>;
        
        if (!entity.id || typeof entity.id !== 'string') {
          errors.push('Subscription entity must contain valid id');
        }
        
        if (!entity.status || typeof entity.status !== 'string') {
          warnings.push('Subscription entity should contain status');
        }
      }
    }
  }
  
  // Validate payment events specifically
  if (typeof webhookData.event === 'string' && webhookData.event.startsWith('payment.')) {
    const payloadObj = webhookData.payload as Record<string, unknown>;
    
    if (!payloadObj.payment) {
      errors.push('Payment events must contain payment data');
    } else {
      const payment = payloadObj.payment as Record<string, unknown>;
      
      if (!payment.entity || typeof payment.entity !== 'object') {
        errors.push('Payment data must contain entity object');
      } else {
        const entity = payment.entity as Record<string, unknown>;
        
        if (!entity.id || typeof entity.id !== 'string') {
          errors.push('Payment entity must contain valid id');
        }
        
        if (!entity.amount || typeof entity.amount !== 'number') {
          warnings.push('Payment entity should contain amount');
        }
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate account ID matches expected account
 */
export function validateAccountId(
  webhookAccountId: string,
  expectedAccountId?: string
): { valid: boolean; error?: string } {
  if (!expectedAccountId) {
    // If no expected account ID is configured, skip validation
    return { valid: true };
  }
  
  if (webhookAccountId !== expectedAccountId) {
    return {
      valid: false,
      error: `Account ID mismatch: received ${webhookAccountId}, expected ${expectedAccountId}`
    };
  }
  
  return { valid: true };
}

/**
 * ENHANCED: Validate webhook business logic based on event type and payload content
 */
export function validateWebhookBusinessLogic(
  webhookData: Record<string, unknown>
): { errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  const eventType = webhookData.event as string;
  const payload = webhookData.payload as Record<string, unknown>;

  if (!eventType) {
    errors.push('Missing event type in webhook payload');
    return { errors, warnings };
  }

  if (!payload) {
    errors.push('Missing payload in webhook data');
    return { errors, warnings };
  }

  // Validate subscription-related events
  if (eventType.startsWith('subscription.')) {
    const subscriptionValidation = validateSubscriptionWebhook(eventType, payload);
    errors.push(...subscriptionValidation.errors);
    warnings.push(...subscriptionValidation.warnings);
  }

  // Validate payment-related events
  if (eventType.startsWith('payment.')) {
    const paymentValidation = validatePaymentWebhook(eventType, payload);
    errors.push(...paymentValidation.errors);
    warnings.push(...paymentValidation.warnings);
  }

  // Validate invoice-related events
  if (eventType.startsWith('invoice.')) {
    const invoiceValidation = validateInvoiceWebhook(eventType, payload);
    errors.push(...invoiceValidation.errors);
    warnings.push(...invoiceValidation.warnings);
  }

  return { errors, warnings };
}

/**
 * Validate subscription webhook events
 */
function validateSubscriptionWebhook(
  eventType: string,
  payload: Record<string, unknown>
): { errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  const subscription = payload.subscription as Record<string, unknown>;

  if (!subscription || !subscription.entity) {
    errors.push('Missing subscription entity in payload');
    return { errors, warnings };
  }

  const entity = subscription.entity as Record<string, unknown>;

  // Validate required fields
  if (!entity.id) {
    errors.push('Missing subscription ID in entity');
  }

  if (!entity.status) {
    errors.push('Missing subscription status in entity');
  }

  if (!entity.plan_id) {
    warnings.push('Missing plan_id in subscription entity');
  }

  // Validate status transitions
  const status = entity.status as string;
  const validStatuses = ['created', 'authenticated', 'active', 'pending', 'halted', 'cancelled', 'completed', 'expired'];

  if (status && !validStatuses.includes(status)) {
    warnings.push(`Unknown subscription status: ${status}`);
  }

  // Event-specific validations
  switch (eventType) {
    case 'subscription.activated':
      if (status !== 'active') {
        warnings.push(`Subscription activated event but status is ${status}, expected 'active'`);
      }
      break;

    case 'subscription.cancelled':
      if (status !== 'cancelled') {
        warnings.push(`Subscription cancelled event but status is ${status}, expected 'cancelled'`);
      }
      break;

    case 'subscription.charged':
      if (!payload.payment) {
        errors.push('Missing payment data in subscription.charged event');
      }
      break;

    case 'subscription.completed':
      if (status !== 'completed') {
        warnings.push(`Subscription completed event but status is ${status}, expected 'completed'`);
      }
      break;
  }

  return { errors, warnings };
}

/**
 * Validate payment webhook events
 */
function validatePaymentWebhook(
  eventType: string,
  payload: Record<string, unknown>
): { errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  const payment = payload.payment as Record<string, unknown>;

  if (!payment || !payment.entity) {
    errors.push('Missing payment entity in payload');
    return { errors, warnings };
  }

  const entity = payment.entity as Record<string, unknown>;

  // Validate required fields
  if (!entity.id) {
    errors.push('Missing payment ID in entity');
  }

  if (!entity.status) {
    errors.push('Missing payment status in entity');
  }

  if (!entity.amount) {
    warnings.push('Missing payment amount in entity');
  }

  // Validate status
  const status = entity.status as string;
  const validStatuses = ['created', 'authorized', 'captured', 'refunded', 'failed'];

  if (status && !validStatuses.includes(status)) {
    warnings.push(`Unknown payment status: ${status}`);
  }

  // Event-specific validations
  switch (eventType) {
    case 'payment.authorized':
      if (status !== 'authorized') {
        warnings.push(`Payment authorized event but status is ${status}, expected 'authorized'`);
      }
      break;

    case 'payment.captured':
      if (status !== 'captured') {
        warnings.push(`Payment captured event but status is ${status}, expected 'captured'`);
      }
      break;

    case 'payment.failed':
      if (status !== 'failed') {
        warnings.push(`Payment failed event but status is ${status}, expected 'failed'`);
      }
      break;
  }

  return { errors, warnings };
}

/**
 * Validate invoice webhook events
 */
function validateInvoiceWebhook(
  eventType: string,
  payload: Record<string, unknown>
): { errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  const invoice = payload.invoice as Record<string, unknown>;

  if (!invoice || !invoice.entity) {
    errors.push('Missing invoice entity in payload');
    return { errors, warnings };
  }

  const entity = invoice.entity as Record<string, unknown>;

  // Validate required fields
  if (!entity.id) {
    errors.push('Missing invoice ID in entity');
  }

  if (!entity.status) {
    errors.push('Missing invoice status in entity');
  }

  // Validate status
  const status = entity.status as string;
  const validStatuses = ['draft', 'issued', 'partially_paid', 'paid', 'cancelled', 'expired'];

  if (status && !validStatuses.includes(status)) {
    warnings.push(`Unknown invoice status: ${status}`);
  }

  // Event-specific validations
  switch (eventType) {
    case 'invoice.paid':
      if (status !== 'paid') {
        warnings.push(`Invoice paid event but status is ${status}, expected 'paid'`);
      }
      if (!payload.payment) {
        errors.push('Missing payment data in invoice.paid event');
      }
      break;
  }

  return { errors, warnings };
}

/**
 * ENHANCED: Comprehensive webhook validation with business logic checks
 */
export function validateWebhook(
  payload: unknown,
  expectedAccountId?: string
): WebhookValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate payload structure
  const payloadValidation = validateWebhookPayload(payload);
  errors.push(...payloadValidation.errors);
  warnings.push(...payloadValidation.warnings);

  if (payloadValidation.valid && payload) {
    const webhookData = payload as Record<string, unknown>;

    // Validate account ID
    if (typeof webhookData.account_id === 'string') {
      const accountValidation = validateAccountId(webhookData.account_id, expectedAccountId);
      if (!accountValidation.valid && accountValidation.error) {
        errors.push(accountValidation.error);
      }
    }

    // ENHANCED: Validate webhook timestamp for replay attack prevention
    if (typeof webhookData.created_at === 'number') {
      const timestampValidation = validateWebhookTimestamp(webhookData.created_at);
      if (!timestampValidation.valid && timestampValidation.error) {
        warnings.push(`Timestamp validation: ${timestampValidation.error}`);
      }
    }

    // ENHANCED: Validate business logic based on event type
    const businessLogicValidation = validateWebhookBusinessLogic(webhookData);
    errors.push(...businessLogicValidation.errors);
    warnings.push(...businessLogicValidation.warnings);
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Extract entity ID from webhook payload
 */
export function extractEntityId(payload: RazorpayWebhookData): string | null {
  try {
    const eventType = payload.event;

    if (eventType.startsWith('subscription.')) {
      return payload.payload.subscription?.id || null;
    }

    if (eventType.startsWith('payment.')) {
      return payload.payload.payment?.entity?.id || null;
    }

    if (eventType.startsWith('invoice.')) {
      return payload.payload.invoice?.entity?.id || null;
    }

    if (eventType.startsWith('refund.')) {
      return payload.payload.refund?.entity?.id || null;
    }

    return null;
  } catch (error) {
    console.error('[WEBHOOK_VALIDATION] Error extracting entity ID:', error);
    return null;
  }
}
