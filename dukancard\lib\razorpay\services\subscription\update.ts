/**
 * Razorpay Subscription Update
 *
 * This file contains functions for updating Razorpay subscriptions.
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { RazorpayApiResponse, RazorpaySubscription, UpdateSubscriptionParams, PauseSubscriptionParams, ResumeSubscriptionParams } from "./types";

/**
 * Update a subscription in Razorpay
 *
 * API Endpoint: PATCH https://api.razorpay.com/v1/subscriptions/:id
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param subscriptionId The subscription ID
 * @param params The update parameters
 * @returns The updated subscription or error
 */
export async function updateSubscription(
  subscriptionId: string,
  params: UpdateSubscriptionParams
): Promise<RazorpayApiResponse<RazorpaySubscription>> {
  try {
    console.log(`[RAZORPAY_DEBUG] Updating subscription with ID: ${subscriptionId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}`, {
      method: 'PATCH',
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error updating subscription:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully updated subscription: ${data.id}`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception updating subscription:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}

/**
 * Pause a subscription in Razorpay
 *
 * API Endpoint: POST https://api.razorpay.com/v1/subscriptions/:id/pause
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param subscriptionId The subscription ID
 * @param pauseAt When to pause the subscription ("now" or "cycle_end")
 * @param isAuthenticated Whether the subscription is in authenticated state
 * @returns The paused subscription or error
 */
export async function pauseSubscription(
  subscriptionId: string,
  pauseAt: "now" | "cycle_end" = "now",
  isAuthenticated: boolean = false
): Promise<RazorpayApiResponse<RazorpaySubscription>> {
  const maxRetries = 3;
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`[RAZORPAY_DEBUG] Pausing subscription with ID: ${subscriptionId} (attempt ${attempt}/${maxRetries})`);

      // Get API headers
      const headers = getRazorpayApiHeaders();

      // Prepare request body - for authenticated subscriptions, don't send pause_at
      const body: PauseSubscriptionParams = isAuthenticated ? {} : { pause_at: pauseAt };

      console.log(`[RAZORPAY_DEBUG] Request body for pause:`, body);

      // Make API request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/pause`, {
        method: 'POST',
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // Parse response
      const data = await response.json();

      if (!response.ok) {
        console.error(`[RAZORPAY_ERROR] Error pausing subscription (attempt ${attempt}):`, data);

        // If it's a client error (4xx), don't retry
        if (response.status >= 400 && response.status < 500) {
          return { success: false, error: data };
        }

        // For server errors (5xx), retry
        lastError = data;
        if (attempt === maxRetries) {
          return { success: false, error: data };
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
        console.log(`[RAZORPAY_DEBUG] Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      console.log(`[RAZORPAY_DEBUG] Successfully paused subscription: ${data.id}`);
      return { success: true, data };
    } catch (error) {
      console.error(`[RAZORPAY_ERROR] Exception pausing subscription (attempt ${attempt}):`, error);
      lastError = error;

      // If it's an abort error, don't retry
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          error: {
            message: 'Request timeout - please try again',
            code: 'TIMEOUT_ERROR',
            type: 'EXCEPTION'
          }
        };
      }

      // For network errors, retry
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
        console.log(`[RAZORPAY_DEBUG] Network error, retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
    }
  }

  // If all retries failed
  return {
    success: false,
    error: {
      message: lastError instanceof Error ? lastError.message : 'Network error after multiple retries',
      code: 'NETWORK_ERROR',
      type: 'EXCEPTION'
    }
  };
}

/**
 * Resume a subscription in Razorpay
 *
 * API Endpoint: POST https://api.razorpay.com/v1/subscriptions/:id/resume
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * Request Parameters:
 * - resume_at: "now" (The value should be now to resume a Subscription immediately)
 *
 * @param subscriptionId The subscription ID
 * @returns The resumed subscription or error
 *
 * Example response:
 * ```json
 * {
 *   "id": "sub_00000000000001",
 *   "entity": "subscription",
 *   "plan_id": "plan_00000000000001",
 *   "status": "active",
 *   "current_start": null,
 *   "current_end": null,
 *   "ended_at": null,
 *   "quantity": 1,
 *   "notes": {
 *     "notes_key_1": "Tea, Earl Grey, Hot",
 *     "notes_key_2": "Tea, Earl Grey… decaf."
 *   },
 *   "charge_at": 1580453311,
 *   "start_at": 1580626111,
 *   "end_at": 1583433000,
 *   "auth_attempts": 0,
 *   "total_count": 6,
 *   "paid_count": 0,
 *   "customer_notify": true,
 *   "created_at": 1580280581,
 *   "paused_at": 1590280581,
 *   "expire_by": 1580626111,
 *   "pause_initiated_by": null,
 *   "short_url": "https://rzp.io/i/z3b1R61A9",
 *   "has_scheduled_changes": false,
 *   "change_scheduled_at": null,
 *   "source": "api",
 *   "offer_id": "offer_JHD834hjbxzhd38d",
 *   "remaining_count": 6
 * }
 * ```
 */
export async function resumeSubscription(
  subscriptionId: string
): Promise<RazorpayApiResponse<RazorpaySubscription>> {
  try {
    console.log(`[RAZORPAY_DEBUG] Resuming subscription with ID: ${subscriptionId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Prepare request body
    const body: ResumeSubscriptionParams = { resume_at: "now" };

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/resume`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error resuming subscription:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully resumed subscription: ${data.id}`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception resuming subscription:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
