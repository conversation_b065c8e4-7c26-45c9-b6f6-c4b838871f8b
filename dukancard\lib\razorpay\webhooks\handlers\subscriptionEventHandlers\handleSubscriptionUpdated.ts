import { RazorpayWebhookData, RazorpaySubscription } from "../../../types/api";
import { SupabaseClient } from "@supabase/supabase-js";
import {
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";

/**
 * Handle subscription.updated event
 *
 * This event is triggered when a subscription is updated.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionUpdated(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract subscription data from payload
    const subscriptionData = payload.payload.subscription;

    if (!subscriptionData || !subscriptionData.entity) {
      console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload");
      return { success: false, message: "Subscription data not found in payload" };
    }

    // Cast to proper type to access properties
    const subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    const subscriptionId = subscription.id;
    console.log(`[RAZORPAY_WEBHOOK] Subscription updated: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.updated',
      eventId: razorpayEventId || `updated_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // For now, we just acknowledge the event.
    // We can add more logic here if needed.
    return { success: true, message: "Subscription update acknowledged" };
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription updated:", error);
    return {
      success: false,
      message: `Error handling subscription updated: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}