"use server";

import { ActionResponse } from "@/lib/types/api";

/**
 * Create a standard error response
 * @param error The error message
 * @returns The error response
 */
export async function createErrorResponse<T = Record<string, unknown>>(error: string): Promise<ActionResponse<T>> {
  return { success: false, message: error, error };
}

/**
 * Create a standard success response
 * @param data The response data
 * @returns The success response
 */
export async function createSuccessResponse<T>(data?: T, message: string = "Success"): Promise<ActionResponse<T>> {
  return { success: true, message, data };
}

/**
 * Create a standard error response (synchronous version)
 * @param error The error message
 * @returns The error response
 */
export async function createErrorResponseSync<T = Record<string, unknown>>(error: string): Promise<ActionResponse<T>> {
  return { success: false, message: error, error };
}

/**
 * Create a standard success response (synchronous version)
 * @param data The response data
 * @returns The success response
 */
export async function createSuccessResponseSync<T>(data?: T, message: string = "Success"): Promise<ActionResponse<T>> {
  return { success: true, message, data };
}
