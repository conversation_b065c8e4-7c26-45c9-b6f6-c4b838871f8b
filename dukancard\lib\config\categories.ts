import { LucideIcon } from "lucide-react";
import {
  Utensils,
  Briefcase,
  GraduationCap,
  Car,
  Store,
  Stethoscope,
  Scissors,
  Wrench,
  ShoppingBag,
  Laptop,
  Home,
  Dumbbell,
  Plane,
  Coffee,
  Music,
  Camera,
  Pen,
  PenTool,
  Code,
  Shirt,
  Truck,
  Building,
  Landmark,
  Hammer,
  Leaf,
  Palette,
  BookOpen,
  Users,
  HeartPulse,
  Sparkles,
  Gem,
  Smartphone,
  Megaphone,
  Banknote,
  Gavel,
  Scroll,
  Handshake,
  Warehouse,
  Factory,
  Tractor,
  Cake,
  Bed,
  Luggage,
  Bus,
  Flower,
  ShoppingCart,
  Brush,
  Mic,
  Film,
  Gamepad2,
  Printer,
  Cog,
  Boxes,
  Wallet,
  BadgeIndianRupee,
  Building2,
  Presentation,
  Zap,
  Droplets,
  Recycle,
  Microscope,
  Pill,
  Baby,
  Glasses,
  Footprints,
  Bike,
  Sofa,
  Stamp,
  ShieldCheck,
  Bug,
} from "lucide-react";

export interface BusinessCategory {
  name: string;
  icon: LucideIcon;
  slug?: string;
  description?: string;
  isPopular?: boolean;
}

/**
 * Shared business categories used across the application
 * This centralized list ensures consistency across different components
 * Some categories are marked as popular for display on homepage and discovery page
 */
export const BUSINESS_CATEGORIES: BusinessCategory[] = [
  // Food & Dining
  { name: "Restaurants", icon: Utensils, slug: "restaurants", description: "Restaurants and dining establishments", isPopular: true },
  { name: "Cafes & Bakeries", icon: Coffee, slug: "cafes-bakeries", description: "Coffee shops, bakeries, and dessert places", isPopular: true },
  { name: "Food Delivery", icon: Truck, slug: "food-delivery", description: "Food delivery services" },
  { name: "Catering", icon: Utensils, slug: "catering", description: "Catering services for events" },
  { name: "Sweet Shops", icon: Cake, slug: "sweet-shops", description: "Traditional sweet shops and confectioneries" },
  { name: "Street Food", icon: Utensils, slug: "street-food", description: "Street food vendors and stalls" },
  { name: "Cloud Kitchen", icon: Utensils, slug: "cloud-kitchen", description: "Delivery-only food businesses" },
  { name: "Tiffin Services", icon: Utensils, slug: "tiffin-services", description: "Home-cooked meal delivery services" },

  // Retail & Shopping
  { name: "Retail Stores", icon: Store, slug: "retail", description: "General retail and shops", isPopular: true },
  { name: "Grocery & Supermarkets", icon: ShoppingCart, slug: "grocery", description: "Grocery stores and supermarkets", isPopular: true },
  { name: "Fashion & Clothing", icon: Shirt, slug: "fashion", description: "Clothing and fashion retailers", isPopular: true },
  { name: "Electronics", icon: Smartphone, slug: "electronics", description: "Electronics and gadget stores" },
  { name: "Home Decor", icon: Sofa, slug: "home-decor", description: "Home decor and furnishing stores" },
  { name: "Jewelry", icon: Gem, slug: "jewelry", description: "Jewelry and accessory stores" },
  { name: "Bookstores", icon: BookOpen, slug: "bookstores", description: "Book shops and stationers" },
  { name: "Footwear", icon: Footprints, slug: "footwear", description: "Shoe and footwear stores" },
  { name: "Gift Shops", icon: ShoppingBag, slug: "gift-shops", description: "Gift and souvenir shops" },
  { name: "Eyewear", icon: Glasses, slug: "eyewear", description: "Optical shops and eyewear retailers" },
  { name: "Mobile Shops", icon: Smartphone, slug: "mobile-shops", description: "Mobile phone retailers and repair shops" },

  // Professional Services
  { name: "Legal Services", icon: Gavel, slug: "legal", description: "Lawyers and legal consultants", isPopular: true },
  { name: "Financial Services", icon: Banknote, slug: "financial", description: "Financial advisors and services", isPopular: true },
  { name: "Accounting", icon: BadgeIndianRupee, slug: "accounting", description: "Accounting and tax services" },
  { name: "Consulting", icon: Briefcase, slug: "consulting", description: "Business and management consulting" },
  { name: "Insurance", icon: Wallet, slug: "insurance", description: "Insurance services and agents" },
  { name: "HR Services", icon: Users, slug: "hr-services", description: "Human resources and recruitment services" },
  { name: "Tax Consultants", icon: BadgeIndianRupee, slug: "tax-consultants", description: "Tax filing and consultation services" },
  { name: "Notary Services", icon: Stamp, slug: "notary", description: "Notary and document verification services" },
  { name: "Translation Services", icon: Scroll, slug: "translation", description: "Language translation and interpretation services" },

  // Healthcare
  { name: "Medical Clinics", icon: Stethoscope, slug: "medical", description: "Medical clinics and doctors", isPopular: true },
  { name: "Dental Care", icon: Sparkles, slug: "dental", description: "Dental clinics and services" },
  { name: "Pharmacy", icon: Pill, slug: "pharmacy", description: "Pharmacies and medical supplies" },
  { name: "Mental Health", icon: HeartPulse, slug: "mental-health", description: "Mental health services and counseling" },
  { name: "Alternative Medicine", icon: Leaf, slug: "alternative-medicine", description: "Ayurveda, homeopathy, and alternative treatments" },
  { name: "Diagnostic Centers", icon: Microscope, slug: "diagnostic", description: "Medical testing and diagnostic centers" },
  { name: "Physiotherapy", icon: HeartPulse, slug: "physiotherapy", description: "Physiotherapy and rehabilitation services" },
  { name: "Veterinary", icon: HeartPulse, slug: "veterinary", description: "Veterinary clinics and pet healthcare" },
  { name: "Elder Care", icon: HeartPulse, slug: "elder-care", description: "Elder care and assisted living services" },
  { name: "Maternity Care", icon: Baby, slug: "maternity", description: "Maternity and childcare services" },

  // Beauty & Wellness
  { name: "Salon & Spa", icon: Scissors, slug: "salon-spa", description: "Beauty salons and spa services", isPopular: true },
  { name: "Fitness", icon: Dumbbell, slug: "fitness", description: "Gyms and fitness centers", isPopular: true },
  { name: "Yoga & Meditation", icon: Users, slug: "yoga", description: "Yoga studios and meditation centers" },
  { name: "Cosmetics", icon: Sparkles, slug: "cosmetics", description: "Cosmetics and beauty products" },
  { name: "Barber Shops", icon: Scissors, slug: "barber", description: "Men's grooming and barber shops" },
  { name: "Wellness Centers", icon: Sparkles, slug: "wellness", description: "Wellness and holistic health centers" },
  { name: "Massage Therapy", icon: Sparkles, slug: "massage", description: "Massage and bodywork services" },
  { name: "Skin Care", icon: Sparkles, slug: "skin-care", description: "Skin care clinics and dermatology" },

  // Education & Training
  { name: "Schools", icon: GraduationCap, slug: "schools", description: "Schools and educational institutions", isPopular: true },
  { name: "Coaching Centers", icon: BookOpen, slug: "coaching", description: "Coaching and tutoring centers" },
  { name: "Vocational Training", icon: Presentation, slug: "vocational", description: "Vocational and skill training" },
  { name: "Online Education", icon: Laptop, slug: "online-education", description: "Online courses and e-learning" },
  { name: "Language Schools", icon: BookOpen, slug: "language", description: "Language learning and training centers" },
  { name: "Music Classes", icon: Music, slug: "music-classes", description: "Music schools and training" },
  { name: "Dance Classes", icon: Music, slug: "dance-classes", description: "Dance schools and training" },
  { name: "Art Schools", icon: Palette, slug: "art-schools", description: "Art and craft education" },
  { name: "Driving Schools", icon: Car, slug: "driving-education", description: "Driving training and education" },
  { name: "Playschools", icon: Baby, slug: "playschools", description: "Preschools and early education" },
  { name: "Tuition Centers", icon: BookOpen, slug: "tuition", description: "Private tutoring and academic support" },

  // Technology
  { name: "IT Services", icon: Code, slug: "it-services", description: "IT services and support", isPopular: true },
  { name: "Software Development", icon: Laptop, slug: "software", description: "Software development companies" },
  { name: "Web Development", icon: Code, slug: "web-development", description: "Web design and development services" },
  { name: "Digital Marketing", icon: Megaphone, slug: "digital-marketing", description: "Digital marketing agencies and services" },
  { name: "App Development", icon: Smartphone, slug: "app-development", description: "Mobile app development services" },
  { name: "IT Hardware", icon: Laptop, slug: "it-hardware", description: "Computer hardware sales and services" },
  { name: "Cyber Security", icon: ShieldCheck, slug: "cyber-security", description: "Cybersecurity services and solutions" },
  { name: "Cloud Services", icon: Code, slug: "cloud-services", description: "Cloud computing and hosting services" },
  { name: "Data Analytics", icon: Code, slug: "data-analytics", description: "Data analysis and business intelligence" },

  // Automotive
  { name: "Auto Repair", icon: Wrench, slug: "auto-repair", description: "Car repair and service centers", isPopular: true },
  { name: "Car Dealerships", icon: Car, slug: "car-dealerships", description: "New and used car dealerships" },
  { name: "Auto Parts", icon: Cog, slug: "auto-parts", description: "Automotive parts and accessories" },
  { name: "Two-Wheeler Services", icon: Bike, slug: "two-wheeler", description: "Motorcycle and scooter services" },
  { name: "Car Wash", icon: Car, slug: "car-wash", description: "Car washing and detailing services" },
  { name: "Tyre Shops", icon: Car, slug: "tyre-shops", description: "Tyre sales and services" },
  { name: "Auto Electricians", icon: Zap, slug: "auto-electricians", description: "Automotive electrical repair services" },
  { name: "Vehicle Rental", icon: Car, slug: "vehicle-rental", description: "Car and bike rental services" },

  // Real Estate & Construction
  { name: "Real Estate", icon: Home, slug: "real-estate", description: "Property and real estate services", isPopular: true },
  { name: "Construction", icon: Hammer, slug: "construction", description: "Construction services and contractors" },
  { name: "Interior Design", icon: Palette, slug: "interior-design", description: "Interior design and decoration services" },
  { name: "Architecture", icon: Building, slug: "architecture", description: "Architectural services and firms" },
  { name: "Property Management", icon: Building, slug: "property-management", description: "Property management services" },
  { name: "Building Materials", icon: Boxes, slug: "building-materials", description: "Construction materials suppliers" },
  { name: "Plumbing Services", icon: Wrench, slug: "plumbing", description: "Plumbing installation and repair" },
  { name: "Electrical Services", icon: Zap, slug: "electrical", description: "Electrical installation and repair" },
  { name: "Painting Services", icon: Brush, slug: "painting", description: "House painting and finishing services" },
  { name: "Carpentry", icon: Hammer, slug: "carpentry", description: "Carpentry and woodworking services" },
  { name: "Landscaping", icon: Flower, slug: "landscaping", description: "Garden and landscape design services" },

  // Travel & Hospitality
  { name: "Hotels", icon: Bed, slug: "hotels", description: "Hotels and accommodations", isPopular: true },
  { name: "Travel Agencies", icon: Plane, slug: "travel-agencies", description: "Travel agencies and tour operators" },
  { name: "Transportation", icon: Bus, slug: "transportation", description: "Transportation services" },
  { name: "Tourism", icon: Luggage, slug: "tourism", description: "Tourism services and attractions" },
  { name: "Homestays", icon: Home, slug: "homestays", description: "Homestays and guest houses" },
  { name: "Tour Guides", icon: Plane, slug: "tour-guides", description: "Local tour guides and services" },
  { name: "Adventure Tourism", icon: Plane, slug: "adventure-tourism", description: "Adventure sports and tourism" },
  { name: "Resorts", icon: Bed, slug: "resorts", description: "Resorts and vacation properties" },
  { name: "Visa Services", icon: Stamp, slug: "visa-services", description: "Visa application and processing services" },

  // Entertainment & Events
  { name: "Entertainment", icon: Music, slug: "entertainment", description: "Entertainment venues and services", isPopular: true },
  { name: "Event Management", icon: Sparkles, slug: "event-management", description: "Event planning and management services" },
  { name: "Wedding Services", icon: Handshake, slug: "wedding", description: "Wedding planning and related services" },
  { name: "Photography", icon: Camera, slug: "photography", description: "Photography and videography services" },
  { name: "Cinema Halls", icon: Film, slug: "cinema", description: "Movie theaters and cinemas" },
  { name: "Gaming Zones", icon: Gamepad2, slug: "gaming", description: "Gaming arcades and entertainment centers" },
  { name: "Party Venues", icon: Music, slug: "party-venues", description: "Party and event venues" },
  { name: "DJs & Musicians", icon: Music, slug: "djs-musicians", description: "DJs and live music performers" },
  { name: "Amusement Parks", icon: Sparkles, slug: "amusement-parks", description: "Amusement and theme parks" },

  // Freelancers & Creative Professionals
  { name: "Freelance Services", icon: Briefcase, slug: "freelance", description: "Independent professionals and freelancers", isPopular: true },
  { name: "Graphic Design", icon: PenTool, slug: "graphic-design", description: "Graphic design services" },
  { name: "Content Creation", icon: Pen, slug: "content-creation", description: "Content writing and creation services" },
  { name: "Art & Crafts", icon: Brush, slug: "art-crafts", description: "Artists and craftspeople" },
  { name: "Music & Performance", icon: Mic, slug: "music-performance", description: "Musicians and performers" },
  { name: "Videography", icon: Film, slug: "videography", description: "Video production and editing services" },
  { name: "Voice Over Artists", icon: Mic, slug: "voice-over", description: "Voice over and narration services" },
  { name: "Translators", icon: Scroll, slug: "translators", description: "Language translation services" },
  { name: "Tutors", icon: BookOpen, slug: "tutors", description: "Private tutors and educators" },
  { name: "Consultants", icon: Briefcase, slug: "consultants", description: "Independent consultants and advisors" },
  { name: "Astrologers", icon: Sparkles, slug: "astrologers", description: "Astrology and horoscope services" },

  // Manufacturing & Industry
  { name: "Manufacturing", icon: Factory, slug: "manufacturing", description: "Manufacturing businesses" },
  { name: "Wholesale", icon: Warehouse, slug: "wholesale", description: "Wholesale suppliers and distributors" },
  { name: "Textiles", icon: Shirt, slug: "textiles", description: "Textile manufacturing and supplies" },
  { name: "Printing", icon: Printer, slug: "printing", description: "Printing services and press" },
  { name: "Packaging", icon: Boxes, slug: "packaging", description: "Packaging materials and services" },
  { name: "Metal Works", icon: Hammer, slug: "metal-works", description: "Metal fabrication and works" },
  { name: "Plastic Products", icon: Factory, slug: "plastic-products", description: "Plastic manufacturing and products" },
  { name: "Handicrafts", icon: Brush, slug: "handicrafts", description: "Handmade crafts and products" },
  { name: "Furniture Making", icon: Sofa, slug: "furniture", description: "Furniture manufacturing and carpentry" },

  // Agriculture
  { name: "Agriculture", icon: Tractor, slug: "agriculture", description: "Farming and agricultural services" },
  { name: "Dairy", icon: Droplets, slug: "dairy", description: "Dairy farms and products" },
  { name: "Organic Products", icon: Leaf, slug: "organic", description: "Organic farming and products" },
  { name: "Poultry", icon: Leaf, slug: "poultry", description: "Poultry farming and products" },
  { name: "Fisheries", icon: Droplets, slug: "fisheries", description: "Fish farming and aquaculture" },
  { name: "Nurseries", icon: Leaf, slug: "nurseries", description: "Plant nurseries and gardening supplies" },
  { name: "Farm Equipment", icon: Tractor, slug: "farm-equipment", description: "Agricultural equipment and supplies" },
  { name: "Seed Suppliers", icon: Leaf, slug: "seed-suppliers", description: "Seeds and agricultural inputs" },
  { name: "Floriculture", icon: Flower, slug: "floriculture", description: "Flower growing and selling" },

  // Utilities & Services
  { name: "Utilities", icon: Zap, slug: "utilities", description: "Utility services" },
  { name: "Cleaning Services", icon: Sparkles, slug: "cleaning", description: "Cleaning and maintenance services" },
  { name: "Waste Management", icon: Recycle, slug: "waste-management", description: "Waste collection and recycling services" },
  { name: "Courier & Logistics", icon: Truck, slug: "logistics", description: "Courier, delivery, and logistics services" },
  { name: "Home Services", icon: Home, slug: "home-services", description: "Home repair and maintenance services" },
  { name: "Pest Control", icon: Bug, slug: "pest-control", description: "Pest control and extermination services" },
  { name: "Security Services", icon: ShieldCheck, slug: "security", description: "Security guards and services" },
  { name: "Laundry Services", icon: Sparkles, slug: "laundry", description: "Laundry and dry cleaning services" },
  { name: "Water Supply", icon: Droplets, slug: "water-supply", description: "Water delivery and supply services" },
  { name: "Rental Services", icon: Boxes, slug: "rental", description: "Equipment and item rental services" },

  // Other Categories
  { name: "Religious Services", icon: Landmark, slug: "religious", description: "Religious institutions and services" },
  { name: "NGOs & Charities", icon: Handshake, slug: "ngo", description: "Non-profit organizations and charities" },
  { name: "Government Services", icon: Building2, slug: "government", description: "Government offices and services" },
  { name: "Repair Services", icon: Wrench, slug: "repair", description: "General repair and maintenance services" },
  { name: "Tailoring", icon: Scissors, slug: "tailoring", description: "Tailoring and alteration services" },
  { name: "Printing & Copying", icon: Printer, slug: "printing-copying", description: "Printing, copying, and document services" },
  { name: "Astrology", icon: Sparkles, slug: "astrology", description: "Astrology and spiritual services" },
  { name: "Funeral Services", icon: Landmark, slug: "funeral", description: "Funeral homes and memorial services" },
  { name: "Daycare", icon: Baby, slug: "daycare", description: "Childcare and daycare services" },
  { name: "Pet Services", icon: HeartPulse, slug: "pet-services", description: "Pet grooming, boarding, and care" },
  { name: "Other Services", icon: Briefcase, slug: "other", description: "Other business services not listed elsewhere" },
];

/**
 * Get a subset of categories
 * @param count Number of categories to return
 * @returns Array of categories limited to the specified count
 */
export function getCategories(count?: number): BusinessCategory[] {
  if (count && count > 0 && count < BUSINESS_CATEGORIES.length) {
    return BUSINESS_CATEGORIES.slice(0, count);
  }
  return BUSINESS_CATEGORIES;
}

/**
 * Get popular categories
 * @param count Maximum number of popular categories to return
 * @returns Array of popular categories
 */
export function getPopularCategories(count?: number): BusinessCategory[] {
  const popularCategories = BUSINESS_CATEGORIES.filter(category => category.isPopular);
  if (count && count > 0 && count < popularCategories.length) {
    return popularCategories.slice(0, count);
  }
  return popularCategories;
}

/**
 * Get a category by slug
 * @param slug The category slug to find
 * @returns The category object or undefined if not found
 */
export function getCategoryBySlug(slug: string): BusinessCategory | undefined {
  return BUSINESS_CATEGORIES.find(
    (category) => category.slug === slug
  );
}

/**
 * Get a category by name
 * @param name The category name to find
 * @returns The category object or undefined if not found
 */
export function getCategoryByName(name: string): BusinessCategory | undefined {
  return BUSINESS_CATEGORIES.find(
    (category) => category.name.toLowerCase() === name.toLowerCase()
  );
}

/**
 * Category groups with their respective categories
 * This is manually defined to match the comments in the BUSINESS_CATEGORIES array
 */
export const CATEGORY_GROUPS = [
  {
    name: "Food & Dining",
    categories: BUSINESS_CATEGORIES.slice(0, 8)
  },
  {
    name: "Retail & Shopping",
    categories: BUSINESS_CATEGORIES.slice(8, 19)
  },
  {
    name: "Professional Services",
    categories: BUSINESS_CATEGORIES.slice(19, 28)
  },
  {
    name: "Healthcare",
    categories: BUSINESS_CATEGORIES.slice(28, 38)
  },
  {
    name: "Beauty & Wellness",
    categories: BUSINESS_CATEGORIES.slice(38, 46)
  },
  {
    name: "Education & Training",
    categories: BUSINESS_CATEGORIES.slice(46, 57)
  },
  {
    name: "Technology",
    categories: BUSINESS_CATEGORIES.slice(57, 66)
  },
  {
    name: "Automotive",
    categories: BUSINESS_CATEGORIES.slice(66, 75)
  },
  {
    name: "Real Estate & Construction",
    categories: BUSINESS_CATEGORIES.slice(75, 86)
  },
  {
    name: "Travel & Hospitality",
    categories: BUSINESS_CATEGORIES.slice(86, 95)
  },
  {
    name: "Entertainment & Events",
    categories: BUSINESS_CATEGORIES.slice(95, 104)
  },
  {
    name: "Freelancers & Creative Professionals",
    categories: BUSINESS_CATEGORIES.slice(104, 115)
  },
  {
    name: "Manufacturing & Industry",
    categories: BUSINESS_CATEGORIES.slice(115, 124)
  },
  {
    name: "Agriculture",
    categories: BUSINESS_CATEGORIES.slice(124, 133)
  },
  {
    name: "Utilities & Services",
    categories: BUSINESS_CATEGORIES.slice(133, 143)
  },
  {
    name: "Other Categories",
    categories: BUSINESS_CATEGORIES.slice(143)
  }
];
