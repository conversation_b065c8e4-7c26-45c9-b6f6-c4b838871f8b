"use client";

import { createClient } from "@/utils/supabase/client";

/**
 * Get city suggestions based on a search query
 * @param query The search query (minimum 2 characters)
 * @returns Array of city suggestions
 */
/**
 * Get city suggestions based on a search query
 *
 * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.
 * The PostgreSQL function is defined as:
 *
 * ```sql
 * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)
 * RETURNS TABLE(city TEXT) AS $$
 * BEGIN
 *   RETURN QUERY
 *   SELECT DISTINCT "DivisionName" as city
 *   FROM pincodes
 *   WHERE "DivisionName" ILIKE search_query
 *   ORDER BY "DivisionName"
 *   LIMIT result_limit;
 * END;
 * $$ LANGUAGE plpgsql;
 * ```
 *
 * @param query The search query (minimum 2 characters)
 * @returns Array of up to 5 unique city suggestions
 */
export async function getCitySuggestionsClient(query: string): Promise<{
  cities?: string[];
  error?: string;
}> {
  if (!query || query.length < 2) {
    return { error: "Query must be at least 2 characters." };
  }

  const supabase = createClient();
  try {
    // Use the PostgreSQL function to get distinct cities (up to 5)
    const { data: cityData, error: cityError } = await supabase
      .rpc('get_distinct_cities', {
        search_query: `%${query}%`,
        result_limit: 5
      });

    if (cityError) {
      console.error("City Suggestions Error:", cityError);

      // Fallback to regular query if RPC fails (in case the function doesn't exist)
      try {
        // Use DISTINCT ON to get unique city names directly from the database
        const { data: fallbackData, error: fallbackError } = await supabase
          .from("pincodes")
          .select("DivisionName")
          .ilike("DivisionName", `%${query}%`)
          .order("DivisionName")
          .limit(100);

        if (fallbackError) {
          throw fallbackError;
        }

        if (!fallbackData || fallbackData.length === 0) {
          return { cities: [] };
        }

        // Get unique cities and format them
        const cities = [...new Set(fallbackData.map((item) =>
          item.DivisionName.toLowerCase().replace(/\b\w/g, (char: string) => char.toUpperCase())
        ))] as string[];

        return { cities: cities.slice(0, 5) };
      } catch (fallbackErr) {
        console.error("Fallback City Query Error:", fallbackErr);
        return { error: "Database error fetching city suggestions." };
      }
    }

    if (!cityData || cityData.length === 0) {
      return { cities: [] };
    }

    // Format the city names to Title Case
    // The response from get_distinct_cities is an array of objects with a 'city' property
    const cities = cityData.map((item: { city: string }) =>
      item.city.toLowerCase().replace(/\b\w/g, (char: string) => char.toUpperCase())
    );

    return { cities };
  } catch (e) {
    console.error("City Suggestions Exception:", e);
    return { error: "An unexpected error occurred during city suggestions." };
  }
}

/**
 * Get pincode details including city, state, and localities
 * @param pincode The 6-digit pincode
 * @returns Pincode details including city, state, and localities
 */
export async function getPincodeDetailsClient(pincode: string): Promise<{
  city?: string;
  state?: string;
  localities?: string[];
  error?: string;
}> {
  if (!pincode || !/^\d{6}$/.test(pincode)) {
    return { error: "Invalid Pincode format." };
  }

  const supabase = createClient();
  try {
    // First get city and state from pincodes table
    const { data: pincodeData, error: pincodeError } = await supabase
      .from("pincodes")
      .select("OfficeName, DivisionName, StateName")
      .eq("Pincode", pincode)
      .order("OfficeName");

    if (pincodeError) {
      console.error("Pincode Fetch Error:", pincodeError);
      return { error: "Database error fetching pincode details." };
    }

    if (!pincodeData || pincodeData.length === 0) {
      return { error: "Pincode not found." };
    }

    // State names are already in title case format in the database
    const state = pincodeData[0].StateName;

    // Format city name (DivisionName) to Title Case
    const city = pincodeData[0].DivisionName
      .toLowerCase()
      .replace(/\b\w/g, (char: string) => char.toUpperCase());

    // Get unique localities from post office names
    const localities = [
      ...new Set(
        pincodeData.map((item) =>
          item.OfficeName.replace(" B.O", "").trim()
        )
      ),
    ] as string[];

    return {
      city,
      state,
      localities
    };
  } catch (e) {
    console.error("Pincode Lookup Exception:", e);
    return { error: "An unexpected error occurred during pincode lookup." };
  }
}

/**
 * Get city details including state and check if it belongs to a specific state
 * @param city The city name
 * @param expectedState Optional state name to validate against
 * @returns City details including state and validation result
 */
export async function getCityDetailsClient(city: string, expectedState?: string): Promise<{
  state?: string;
  isValidState?: boolean;
  error?: string;
}> {
  if (!city || city.length < 2) {
    return { error: "City name must be at least 2 characters." };
  }

  const supabase = createClient();
  try {
    // Get state for the city - DivisionName is the city column
    // Use exact matching for city name to ensure we get the correct city
    const { data: cityData, error: cityError } = await supabase
      .from("pincodes")
      .select("StateName, DivisionName")
      .eq("DivisionName", city)
      .limit(1);

    if (cityError) {
      console.error("City Fetch Error:", cityError);
      return { error: "Database error fetching city details." };
    }

    if (!cityData || cityData.length === 0) {
      // Try with case-insensitive search as fallback
      const { data: fallbackData, error: fallbackError } = await supabase
        .from("pincodes")
        .select("StateName, DivisionName")
        .ilike("DivisionName", city)
        .limit(1);

      if (fallbackError || !fallbackData || fallbackData.length === 0) {
        return { error: "City not found." };
      }

      // State names are already in title case format in the database
      const state = fallbackData[0].StateName;

      // Check if the city belongs to the expected state
      const isValidState = expectedState ? state === expectedState : true;

      return {
        state,
        isValidState
      };
    }

    // State names are already in title case format in the database
    const state = cityData[0].StateName;

    // Check if the city belongs to the expected state
    const isValidState = expectedState ? state === expectedState : true;

    return {
      state,
      isValidState
    };
  } catch (e) {
    console.error("City Lookup Exception:", e);
    return { error: "An unexpected error occurred during city lookup." };
  }
}
