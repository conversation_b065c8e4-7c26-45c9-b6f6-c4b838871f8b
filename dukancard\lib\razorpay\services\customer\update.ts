/**
 * Update a customer in Razorpay
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../../utils/auth";
import { CustomerApiResponse, UpdateCustomerParams } from "./types";

/**
 * Update a customer in Razorpay
 *
 * API Endpoint: PATCH https://api.razorpay.com/v1/customers/:id
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param customerId The customer ID
 * @param params The update parameters
 * @returns The updated customer or error
 *
 * Example usage:
 * ```
 * const result = await updateCustomer('cust_00000000000001', {
 *   name: '<PERSON>',
 *   email: '<EMAIL>',
 *   contact: '9999999999',
 *   notes: {
 *     note_key_1: 'Note value 1',
 *     note_key_2: 'Note value 2'
 *   }
 * });
 * ```
 */
export async function updateCustomer(
  customerId: string,
  params: UpdateCustomerParams
): Promise<CustomerApiResponse> {
  try {
    console.log(`[RAZORPAY_DEBUG] Updating customer with ID: ${customerId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/customers/${customerId}`, {
      method: 'PATCH',
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error updating customer:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully updated customer: ${data.id}`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception updating customer:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
