export interface WebhookProcessingContext {
  subscriptionId: string;
  eventType: string;
  eventId: string;
  payload: Record<string, unknown>;
  webhookTimestamp: number; // Unix timestamp from Razorpay
}

export interface SubscriptionState {
  id: string;
  business_profile_id: string;
  subscription_status: string;
  plan_id: string;
  cancelled_at: string | null;
  razorpay_subscription_id: string;
  updated_at: string;
  last_webhook_timestamp: string | null;
}