/**
 * Razorpay Plan Service
 *
 * This file contains functions for managing Razorpay plans:
 * - Fetching plan details
 * - Listing plans
 */

import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../utils/auth";

// Razorpay API response type
export interface RazorpayApiResponse<T> {
  success: boolean;
  data?: T;
  error?: unknown;
}

// Razorpay plan item interface
export interface RazorpayPlanItem {
  id: string;
  active: boolean;
  name: string;
  description: string;
  amount: number;
  unit_amount: number;
  currency: string;
  type: string;
  unit: null | string;
  tax_inclusive: boolean;
  hsn_code: null | string;
  sac_code: null | string;
  tax_rate: null | number;
  tax_id: null | string;
  tax_group_id: null | string;
  created_at: number;
  updated_at: number;
}

// Razorpay plan interface
export interface RazorpayPlan {
  id: string;
  entity: string;
  interval: number;
  period: string;
  item: RazorpayPlanItem;
  notes: Record<string, string>;
  created_at: number;
}

/**
 * Get a plan from Razorpay by ID
 *
 * API Endpoint: GET https://api.razorpay.com/v1/plans/:id
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param planId The plan ID
 * @returns The plan details or error
 *
 * Example response:
 * ```json
 * {
 *   "id":"plan_00000000000001",
 *   "entity":"plan",
 *   "interval":1,
 *   "period":"weekly",
 *   "item":{
 *     "id":"item_00000000000001",
 *     "active":true,
 *     "name":"Test plan - Weekly",
 *     "description":"Description for the test plan - Weekly",
 *     "amount":69900,
 *     "unit_amount":69900,
 *     "currency":"INR",
 *     "type":"plan",
 *     "unit":null,
 *     "tax_inclusive":false,
 *     "hsn_code":null,
 *     "sac_code":null,
 *     "tax_rate":null,
 *     "tax_id":null,
 *     "tax_group_id":null,
 *     "created_at":1580220492,
 *     "updated_at":1580220492
 *   },
 *   "notes":{
 *     "notes_key_1":"Tea, Earl Grey, Hot",
 *     "notes_key_2":"Tea, Earl Grey… decaf."
 *   },
 *   "created_at":1580220492
 * }
 * ```
 */
export async function getPlan(
  planId: string
): Promise<RazorpayApiResponse<RazorpayPlan>> {
  try {
    console.log(`[RAZORPAY_DEBUG] Fetching plan with ID: ${planId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/plans/${planId}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching plan:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully fetched plan: ${data.id}`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching plan:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}

/**
 * List all plans from Razorpay
 *
 * API Endpoint: GET https://api.razorpay.com/v1/plans
 *
 * Required Headers:
 * - Authorization: Basic base64(key_id:key_secret)
 *
 * @param params Optional query parameters (count, skip, etc.)
 * @returns List of plans or error
 */
export async function listPlans(
  params: Record<string, string | number> = {}
): Promise<RazorpayApiResponse<{ entity: string; count: number; items: RazorpayPlan[] }>> {
  try {
    console.log('[RAZORPAY_DEBUG] Fetching plans list');

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Build query string
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    // Make API request
    const url = `${RAZORPAY_API_URL.replace('/v2', '/v1')}/plans${queryString ? `?${queryString}` : ''}`;
    const response = await fetch(url, {
      method: 'GET',
      headers
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_ERROR] Error fetching plans:', data);
      return { success: false, error: data };
    }

    console.log(`[RAZORPAY_DEBUG] Successfully fetched ${data.count} plans`);
    return { success: true, data };
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Exception fetching plans:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'EXCEPTION'
      }
    };
  }
}
