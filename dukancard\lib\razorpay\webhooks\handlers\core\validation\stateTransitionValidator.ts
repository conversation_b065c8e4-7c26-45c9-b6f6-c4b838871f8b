import { WebhookProcessingContext, SubscriptionState } from "../types";

export class StateTransitionValidator {

  validateWebhookSequence(
    context: WebhookProcessingContext,
    currentState: SubscriptionState
  ): { should: boolean; reason: string } {
    const validTransitions = this.getValidStateTransitions();
    const isValidTransition = this.isValidStateTransition(
      currentState.subscription_status,
      context.eventType,
      currentState.plan_id,
      validTransitions
    );

    if (!isValidTransition.valid) {
      console.warn(`[STATE_SEQUENCE] REJECTED: ${isValidTransition.reason}`);
      return {
        should: false,
        reason: `STATE REJECTION: ${isValidTransition.reason} - ${isValidTransition.businessRule}`
      };
    }

    return {
      should: true,
      reason: `STATE APPROVED: Valid transition from ${currentState.subscription_status} via ${context.eventType} - ${isValidTransition.businessRule}`
    };
  }

  private getValidStateTransitions(): Record<string, { allowedEvents: string[]; description: string }> {
    return {
      'trial': {
        allowedEvents: [
          'subscription.authenticated',
          'subscription.activated',
          'subscription.cancelled',
          'subscription.expired',
          'subscription.updated'
        ],
        description: 'Trial users can authenticate, activate, cancel, or expire'
      },
      'authenticated': {
        allowedEvents: [
          'subscription.activated',
          'subscription.cancelled',
          'subscription.halted',
          'subscription.charged',
          'subscription.expired',
          'subscription.updated'
        ],
        description: 'Authenticated subscriptions can activate, cancel, halt, charge, or expire'
      },
      'active': {
        allowedEvents: [
          'subscription.charged',
          'subscription.cancelled',
          'subscription.halted',
          'subscription.expired',
          'subscription.completed',
          'subscription.updated'
        ],
        description: 'Active subscriptions can be charged, cancelled, halted, expired, or completed'
      },
      'pending': {
        allowedEvents: [
          'subscription.authenticated',
          'subscription.activated',
          'subscription.cancelled',
          'subscription.expired',
          'subscription.updated'
        ],
        description: 'Pending subscriptions can authenticate, activate, cancel, or expire'
      },
      'halted': {
        allowedEvents: [
          'subscription.cancelled',
          'subscription.expired',
          'subscription.activated', // Can be reactivated
          'subscription.updated'
        ],
        description: 'Halted subscriptions can be cancelled, expired, or reactivated'
      },
      'cancelled': {
        allowedEvents: [],
        description: 'TERMINAL: Cancelled subscriptions cannot transition to any other state'
      },
      'expired': {
        allowedEvents: [],
        description: 'TERMINAL: Expired subscriptions cannot transition to any other state'
      },
      'completed': {
        allowedEvents: [],
        description: 'TERMINAL: Completed subscriptions cannot transition to any other state'
      }
    };
  }

  private isValidStateTransition(
    currentStatus: string,
    incomingEvent: string,
    currentPlan: string,
    validTransitions: Record<string, { allowedEvents: string[]; description: string }>
  ): { valid: boolean; reason: string; businessRule: string } {
    if (currentStatus === 'trial' && currentPlan !== 'free') {
      if (incomingEvent === 'subscription.authenticated') {
        // Allow authentication for trial users with selected plans
      }
    }

    if (currentStatus === 'trial' && currentPlan === 'free') {
      const transitions = validTransitions[currentStatus];
      if (transitions && transitions.allowedEvents.includes(incomingEvent)) {
        return {
          valid: true,
          reason: 'Valid transition for fresh trial user',
          businessRule: transitions.description
        };
      }
    }

    const transitions = validTransitions[currentStatus];

    if (!transitions) {
      return {
        valid: false,
        reason: `Unknown current state: ${currentStatus}`,
        businessRule: 'State not defined in transition matrix'
      };
    }

    if (transitions.allowedEvents.length === 0) {
      return {
        valid: false,
        reason: `Terminal state reached: ${currentStatus}`,
        businessRule: transitions.description
      };
    }

    if (!transitions.allowedEvents.includes(incomingEvent)) {
      return {
        valid: false,
        reason: `Invalid transition from ${currentStatus} via ${incomingEvent}`,
        businessRule: `${transitions.description}. Allowed events: ${transitions.allowedEvents.join(', ')}`
      };
    }

    return {
      valid: true,
      reason: `Valid transition from ${currentStatus} via ${incomingEvent}`,
      businessRule: transitions.description
    };
  }
}

export const stateTransitionValidator = new StateTransitionValidator();